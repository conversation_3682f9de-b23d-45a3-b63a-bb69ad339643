﻿using Dolo.Core.Discord;
namespace Dolo.Bot.Pixi.Hub.Handler;

public static class MessageCreated
{
    public static async Task InvokeAsync(this MessageCreatedEventArgs e)
    {
        if (e.Message.Content.StartsWith("> msp"))
        {
            await e.Message.Channel.TrySendMessageAsync($"Dear {e.Author.Mention}!\n\nOur msp bot has been redeveloped we will use slash commands from now on\nplease use `/msp help` to view all commands.{HubEmoji.MspHeart}\n\n" + $"{(e.Guild.Id != Hub.Pluto?.Id ? $"If slashcommands do not work, simply re-authenticate.\n{Hub.BotInviteUrl}" : string.Empty)}");
            return;
        }

        // check if guild is the main guild
        if (e.Guild?.Id == Hub.Pluto?.Id)
            return;

        // return if length is 0 or tts
        if (e.Message.Content.Length == 0 || e.Message.IsTTS)
            return;

        await HubEmbed.LogAsync(e.Guild, e.Author, e.Message.Content);
    }
}