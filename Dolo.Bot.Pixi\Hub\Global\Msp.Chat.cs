﻿using Dolo.Core;
using Dolo.Core.Discord;
using Dolo.Planet;
using System.Text;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("chat")]
    [Description("view current chat locks")]
    public async Task ChatAsync(SlashCommandContext ctx)
    {
        await ctx.LogAsync("/msp chat");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if shard is null
        if (Hub.MspShard is null)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Please use `/msp init` to initialize.**");
            return;
        }

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » **Checking chat-locks...**");

        // init a string builder
        var builder = new StringBuffer();

        // create a list of tasks
        var tasks = Hub.MspShard.GetAll()
        .Select(async shard =>
            {
                // check if user logged in
                if (!shard.User.LoggedIn)
                {
                    builder.AppendLine($"{MspClientUtil.GetServerDiscordFlag(shard.User.Server)} » unknown » **FAILED**");
                    return;
                }

                // get piggy bank to check server connection
                var chatPermission = await shard.GetChatPermissionsAsync();

                // if worked print yes emote otherwise no
                builder.AppendLine(chatPermission.HasChatLock
                                       ? $"{MspClientUtil.GetServerDiscordFlag(shard.User.Server)} » {HubEmoji.Yes} » **will be unlocked** {chatPermission}"
                                       : $"{MspClientUtil.GetServerDiscordFlag(shard.User.Server)} » {HubEmoji.No} » **will be locked** {chatPermission}");

            });

        await Task.WhenAll(tasks);
        await builder
            .AppendLineAsync("**We made a mini-tool that allows you to\nunlock the chat-locks. check it out on [our server](https://discord.gg/dolo)**");

        await ctx.TryEditResponseAsync(await HubEmbed.ChatAsync(ctx.Guild!, builder.ToBuilder()));
    }
}