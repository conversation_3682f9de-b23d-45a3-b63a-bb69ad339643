using Microsoft.AspNetCore.Components;
using Dolo.Pluto.Web.Hub.OAuth;
using Dolo.Pluto.Web.Apps.Pluto.Auth;

namespace Dolo.Pluto.Web.Apps.Pluto.Content.Shop;

public partial class Shop : ComponentBase
{
    [Inject] private PlutoService PlutoService { get; set; } = default!;
    [Inject] private ShopService ShopService { get; set; } = default!;
    [Inject] private StateService StateService { get; set; } = default!;
    [Inject] private OAuthService OAuthService { get; set; } = default!;
    [Inject] private AuthService AuthService { get; set; } = default!;
    
    protected override void OnInitialized()
    {
        // Initialize the shop
        ShopService.Shop = this;
     }

    public void StateHasChangedAsync()
    {
        InvokeAsync(StateHasChanged);
    }

    private void NavigateToHome()
    {
        PlutoService.SetNavbarTo(Content.Main.Navbar.NavbarTabType.App);
    }

    private async Task AuthenticateAsync()
    {
        var auth = await AuthService.TryAuthenticateAsync();
        if (auth.IsAuthenticated)
        {
            OAuthService.OAuthResult = auth.OAuthData;
            StateHasChanged();
        }
    }
}
