<Project Sdk="Microsoft.NET.Sdk">

    <Import Condition=" '$(EAZFUSCATOR_NET_HOME)' != '' and Exists('$(EAZFUSCATOR_NET_HOME)\Integration\MSBuild\Eazfuscator.NET.targets') " Project="$(EAZFUSCATOR_NET_HOME)\Integration\MSBuild\Eazfuscator.NET.targets" />

    <PropertyGroup>
       <TargetFramework>net10.0-windows</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <OutputType>Exe</OutputType>
        <Description>- pluton - cydolo - cbkdz -</Description>
        <LangVersion>preview</LangVersion>
        <UseWindowsForms>true</UseWindowsForms>
    </PropertyGroup>

    <PropertyGroup>
        <!-- Eazfuscator.NET is integrated with this project at MSBuild level: https://www.gapotchenko.com/eazfuscator.net/kb/100036 -->
        <EazfuscatorIntegration>MSBuild</EazfuscatorIntegration>
        <EazfuscatorActiveConfiguration>Release</EazfuscatorActiveConfiguration>
        <EazfuscatorCompatibilityVersion>2023.3</EazfuscatorCompatibilityVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Dolo.Database\Dolo.Database.csproj" />
        <ProjectReference Include="..\Dolo.Flash\Dolo.Flash.csproj" />
        <ProjectReference Include="..\Dolo.Nebula\Dolo.Nebula.csproj" />
        <ProjectReference Include="..\Dolo.Planet\Dolo.Planet.csproj" />
        <ProjectReference Include="..\Dolo.Pluto.Shard\Dolo.Pluto.Shard.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="BenchmarkDotNet" Version="0.15.2" />
        <PackageReference Include="BenderProxy" Version="1.1.2" />
        <PackageReference Include="SharpPcap" Version="6.3.1" />
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.10" />
        <PackageReference Include="Titanium.Web.Proxy" Version="3.2.2-beta" />
    </ItemGroup>

</Project>
