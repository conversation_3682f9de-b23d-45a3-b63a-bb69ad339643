using System.Collections.Concurrent;
using System.Diagnostics;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// HIGH-PERFORMANCE monitoring service for request-response pairing engine performance metrics
/// Tracks throughput, latency, and connection health with minimal overhead
/// </summary>
public sealed class PairingPerformanceMonitor : IDisposable
{
    private readonly ILogger<PairingPerformanceMonitor> _logger;
    private readonly Timer _reportingTimer;
    private readonly RequestResponsePairingEngine _pairingEngine;
    private readonly object _metricsLock = new();
    private bool _disposed;

    // HIGH-PERFORMANCE METRICS - Lock-free counters
    private long _requestsPerSecond;
    private long _responsesPerSecond;
    private long _averageLatencyMs;
    private long _peakLatencyMs;
    private long _lastReportTime;

    // THROUGHPUT TRACKING - Sliding window for accurate rates
    private readonly ConcurrentQueue<(DateTime Timestamp, int Requests, int Responses)> _throughputWindow = new();
    private readonly TimeSpan _windowSize = TimeSpan.FromMinutes(1);

    // LATENCY TRACKING - Efficient percentile calculation
    private readonly ConcurrentQueue<long> _latencyWindow = new();
    private readonly int _maxLatencySamples = 1000;

    public PairingPerformanceMonitor(RequestResponsePairingEngine pairingEngine, ILogger<PairingPerformanceMonitor> logger)
    {
        _pairingEngine = pairingEngine;
        _logger = logger;
        _lastReportTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

        // Subscribe to pairing events for real-time metrics
        _pairingEngine.TransactionPaired += OnTransactionPaired;

        // Start performance reporting timer
        _reportingTimer = new Timer(ReportPerformanceMetrics, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

        _logger.LogInformation("🚀 High-performance pairing monitor initialized");
    }

    /// <summary>
    /// OPTIMIZATION: Real-time latency tracking with minimal overhead
    /// </summary>
    private void OnTransactionPaired(RequestResponsePairingEngine sender, TransactionPairedEventArgs e)
    {
        if (_disposed) return;

        var latencyMs = (long)(e.Transaction.EndTime - e.Transaction.StartTime).TotalMilliseconds;
        
        // OPTIMIZATION: Lock-free latency tracking
        _latencyWindow.Enqueue(latencyMs);
        
        // OPTIMIZATION: Maintain sliding window size efficiently
        while (_latencyWindow.Count > _maxLatencySamples)
        {
            _latencyWindow.TryDequeue(out _);
        }

        // OPTIMIZATION: Update peak latency atomically
        var currentPeak = Interlocked.Read(ref _peakLatencyMs);
        if (latencyMs > currentPeak)
        {
            Interlocked.CompareExchange(ref _peakLatencyMs, latencyMs, currentPeak);
        }
    }

    /// <summary>
    /// HIGH-PERFORMANCE: Get current performance metrics with minimal computation
    /// </summary>
    public (double RequestsPerSecond, double ResponsesPerSecond, long AverageLatencyMs, long PeakLatencyMs, int ActiveConnections) GetCurrentMetrics()
    {
        var (activeConnections, pendingTransactions, totalPaired, totalTimeouts, totalRequests) = _pairingEngine.GetStatistics();
        
        return (
            Interlocked.Read(ref _requestsPerSecond),
            Interlocked.Read(ref _responsesPerSecond),
            Interlocked.Read(ref _averageLatencyMs),
            Interlocked.Read(ref _peakLatencyMs),
            activeConnections
        );
    }

    /// <summary>
    /// OPTIMIZATION: Get detailed connection health metrics
    /// </summary>
    public Dictionary<string, ConnectionHealthMetrics> GetConnectionHealth()
    {
        var connectionStats = _pairingEngine.GetConnectionStatistics();
        var result = new Dictionary<string, ConnectionHealthMetrics>();

        foreach (var (connectionId, (pendingRequests, sequence, age)) in connectionStats)
        {
            var health = CalculateConnectionHealth(pendingRequests, age);
            result[connectionId] = new ConnectionHealthMetrics
            {
                ConnectionId = connectionId,
                PendingRequests = pendingRequests,
                SequenceNumber = sequence,
                Age = age,
                Health = health,
                ThroughputScore = CalculateThroughputScore(sequence, age)
            };
        }

        return result;
    }

    /// <summary>
    /// OPTIMIZATION: Efficient performance reporting with batched calculations
    /// </summary>
    private void ReportPerformanceMetrics(object? state)
    {
        if (_disposed) return;

        try
        {
            var now = DateTime.UtcNow;
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var timeDelta = currentTime - Interlocked.Read(ref _lastReportTime);
            
            if (timeDelta <= 0) return;

            // OPTIMIZATION: Calculate throughput from sliding window
            CleanupThroughputWindow(now);
            var (requestRate, responseRate) = CalculateThroughputRates();
            
            // OPTIMIZATION: Calculate latency metrics efficiently
            var (avgLatency, p95Latency) = CalculateLatencyMetrics();

            // OPTIMIZATION: Update metrics atomically
            Interlocked.Exchange(ref _requestsPerSecond, (long)requestRate);
            Interlocked.Exchange(ref _responsesPerSecond, (long)responseRate);
            Interlocked.Exchange(ref _averageLatencyMs, avgLatency);
            Interlocked.Exchange(ref _lastReportTime, currentTime);

            // Get overall statistics
            var (activeConnections, pendingTransactions, totalPaired, totalTimeouts, totalRequests) = _pairingEngine.GetStatistics();

            // Log performance summary
            _logger.LogInformation("📊 Performance: {RequestRate:F1} req/s, {ResponseRate:F1} resp/s, {AvgLatency}ms avg, {P95Latency}ms p95, {ActiveConnections} connections",
                requestRate, responseRate, avgLatency, p95Latency, activeConnections);

            // Log detailed statistics every 5 minutes
            if (currentTime % (5 * 60 * 1000) < 30000) // Within 30s of 5-minute mark
            {
                _logger.LogInformation("📈 Detailed stats: {TotalRequests} total requests, {TotalPaired} paired, {TotalTimeouts} timeouts, {PendingTransactions} pending",
                    totalRequests, totalPaired, totalTimeouts, pendingTransactions);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error in performance reporting");
        }
    }

    private void CleanupThroughputWindow(DateTime now)
    {
        var cutoff = now - _windowSize;
        while (_throughputWindow.TryPeek(out var sample) && sample.Timestamp < cutoff)
        {
            _throughputWindow.TryDequeue(out _);
        }
    }

    private (double RequestRate, double ResponseRate) CalculateThroughputRates()
    {
        var samples = _throughputWindow.ToArray();
        if (samples.Length < 2) return (0, 0);

        var totalRequests = samples.Sum(s => s.Requests);
        var totalResponses = samples.Sum(s => s.Responses);
        var timeSpan = samples.Max(s => s.Timestamp) - samples.Min(s => s.Timestamp);
        
        if (timeSpan.TotalSeconds <= 0) return (0, 0);

        return (totalRequests / timeSpan.TotalSeconds, totalResponses / timeSpan.TotalSeconds);
    }

    private (long Average, long P95) CalculateLatencyMetrics()
    {
        var latencies = _latencyWindow.ToArray();
        if (latencies.Length == 0) return (0, 0);

        var average = (long)latencies.Average();
        var sorted = latencies.OrderBy(x => x).ToArray();
        var p95Index = (int)(sorted.Length * 0.95);
        var p95 = sorted.Length > 0 ? sorted[Math.Min(p95Index, sorted.Length - 1)] : 0;

        return (average, p95);
    }

    private static ConnectionHealth CalculateConnectionHealth(int pendingRequests, TimeSpan age)
    {
        // OPTIMIZATION: Simple health calculation based on pending requests and age
        if (pendingRequests > 100) return ConnectionHealth.Critical;
        if (pendingRequests > 50 || age > TimeSpan.FromMinutes(30)) return ConnectionHealth.Warning;
        if (pendingRequests > 20 || age > TimeSpan.FromMinutes(10)) return ConnectionHealth.Degraded;
        return ConnectionHealth.Healthy;
    }

    private static double CalculateThroughputScore(long sequence, TimeSpan age)
    {
        // OPTIMIZATION: Calculate throughput score (requests per minute)
        return age.TotalMinutes > 0 ? sequence / age.TotalMinutes : 0;
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _reportingTimer?.Dispose();
        
        // Unsubscribe from events
        _pairingEngine.TransactionPaired -= OnTransactionPaired;

        _logger.LogInformation("🔄 Performance monitor disposed");
    }
}

/// <summary>
/// Connection health metrics for monitoring
/// </summary>
public sealed class ConnectionHealthMetrics
{
    public string ConnectionId { get; init; } = string.Empty;
    public int PendingRequests { get; init; }
    public long SequenceNumber { get; init; }
    public TimeSpan Age { get; init; }
    public ConnectionHealth Health { get; init; }
    public double ThroughputScore { get; init; }
}

/// <summary>
/// Connection health status enumeration
/// </summary>
public enum ConnectionHealth
{
    Healthy,
    Degraded,
    Warning,
    Critical
}
