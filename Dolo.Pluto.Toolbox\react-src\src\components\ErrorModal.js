import React, { useEffect, useRef } from 'react';

/**
 * ErrorModal Component
 * 
 * A professional error modal that displays when exceptions occur in the application.
 * Features:
 * - Clean, user-friendly design matching the app's theme
 * - Automatic error reporting to developers
 * - Keyboard accessibility (ESC to close)
 * - Focus management for accessibility
 * - Prevents app crashes by gracefully handling errors
 * 
 * @param {boolean} isVisible - Whether the modal is visible
 * @param {string} errorMessage - The error message to display to the user
 * @param {Function} onClose - Callback function when modal is closed
 */
const ErrorModal = ({ isVisible, errorMessage, onClose }) => {
  const modalRef = useRef(null);
  const closeButtonRef = useRef(null);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && isVisible) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      // Focus the close button when modal opens
      setTimeout(() => {
        closeButtonRef.current?.focus();
      }, 100);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isVisible) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div 
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm"
      onClick={onClose}
    >
      <div 
        ref={modalRef}
        className="relative bg-bg-surface border border-border-l1 rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 ease-out"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border-l1">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
              <svg 
                className="w-6 h-6 text-white" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth="2" 
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
                />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-span-primary">
                Oops! Something went wrong
              </h3>
              <p className="text-sm text-span-muted">
                An unexpected error occurred
              </p>
            </div>
          </div>
          <button
            ref={closeButtonRef}
            onClick={onClose}
            className="text-span-muted hover:text-span-primary transition-colors p-1 rounded-lg hover:bg-bg-hover"
            aria-label="Close error dialog"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-4">
            <p className="text-span-secondary text-sm leading-relaxed">
              We've encountered an unexpected error. Don't worry - this has been automatically reported to our development team and we'll work on fixing it.
            </p>
          </div>

          {/* Error details (collapsible) */}
          <details className="mb-4">
            <summary className="text-xs text-span-muted cursor-pointer hover:text-span-secondary transition-colors select-none">
              Technical details (click to expand)
            </summary>
            <div className="mt-2 p-3 bg-bg-hover rounded-lg border border-border-l1">
              <code className="text-xs text-span-muted font-mono break-all">
                {errorMessage || 'An unexpected error occurred'}
              </code>
            </div>
          </details>

          {/* Status indicator */}
          <div className="flex items-center space-x-2 text-xs text-span-muted mb-4">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Error automatically reported to developers</span>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-border-l1">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-bg-surface"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorModal;
