﻿using Dolo.Bot.Pixi.Hub.Global.Enums;
using Dolo.Core.Discord;
using Dolo.Core.Extension;
using Dolo.Core.Filter;
using Dolo.Planet.Entities;
using Dolo.Planet.Enums;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("anim")]
    [Description("get a animation by the name or id")]
    public async Task AnimationAsync(SlashCommandContext ctx,
        [Description("filter type")] AnimationFilterType type,
        [Description("name or id")] string? animation = null)
    {
        await ctx.LogAsync($"/msp anim {type} {animation}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(Server.Germany);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » **Searching animation ..**");

        // get the id of the user if the connection failed return
        var animations = await msp.GetAnimationsAsync();
        if (!animations.Success)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Interaction failed with response `{animations.GetStatusCode()}`**");
            return;
        }

        // if the animation is not random and the animation is null then return
        if (type != AnimationFilterType.Random && string.IsNullOrEmpty(animation))
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Please specify an animation id or name. (use `/msp anim random` to get a random animation)**");
            return;
        }

        // filter the animation by the type
        var animationResult = type switch
        {
            AnimationFilterType.Random => animations.Shuffle().FirstOrDefault(),
            AnimationFilterType.Id => animations.FirstOrDefault(x =>
                x.Id == (int.TryParse(animation, out var val) ? val : 0)),
            AnimationFilterType.Name => FindBestMatch(animations, animation),
            _ => null
        };

        // if the animation is null then it was not found
        if (animationResult is null)
        {
            var suggestions = GetSuggestions(animations, animation).ToList();
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **No animation found with `{animation}`**" +
                (suggestions.Any() ? $"\n**Suggestions:** {string.Join(", ", suggestions.Take(3).Select(a => $"`{a.Name}`"))}" : ""));
            return;
        }

        // send response
        await ctx.TryEditResponseAsync(await HubEmbed.AnimationAsync(ctx.Guild!, animationResult));
    }

    private static MspAnimation? FindBestMatch(IEnumerable<MspAnimation> animations, string? search, int maxResults = 1)
    {
        if (string.IsNullOrWhiteSpace(search)) return null;
        
        var searchTerm = search.Trim().ToLowerInvariant();
        var candidates = animations.Where(a => !string.IsNullOrEmpty(a.Name))
            .Select(a => new
            {
                Animation = a,
                Similarity = GetCosineSimilarity(searchTerm, a.Name!.ToLowerInvariant())
            })
            .OrderByDescending(x => x.Similarity)
            .Take(maxResults);

        return candidates.FirstOrDefault()?.Animation;
    }

    private static IEnumerable<MspAnimation> GetSuggestions(IEnumerable<MspAnimation> animations, string? search)
    {
        if (string.IsNullOrWhiteSpace(search)) return Array.Empty<MspAnimation>();
        
        return animations.Where(a => !string.IsNullOrEmpty(a.Name))
            .Select(a => new
            {
                Animation = a,
                Similarity = GetCosineSimilarity(search.ToLowerInvariant(), a.Name!.ToLowerInvariant())
            })
            .Where(x => x.Similarity > 0.3)
            .OrderByDescending(x => x.Similarity)
            .Take(3)
            .Select(x => x.Animation);
    }

    private static double GetCosineSimilarity(string text1, string text2)
    {
        var vector1 = GetTermFrequencyVector(text1);
        var vector2 = GetTermFrequencyVector(text2);

        double dotProduct = 0;
        double norm1 = 0;
        double norm2 = 0;

        foreach (var term in vector1.Keys.Union(vector2.Keys))
        {
            var val1 = vector1.GetValueOrDefault(term, 0);
            var val2 = vector2.GetValueOrDefault(term, 0);
            dotProduct += val1 * val2;
            norm1 += val1 * val1;
            norm2 += val2 * val2;
        }

        return norm1 > 0 && norm2 > 0 ? dotProduct / (Math.Sqrt(norm1) * Math.Sqrt(norm2)) : 0;
    }

    private static Dictionary<string, double> GetTermFrequencyVector(string text)
    {
        var terms = text.Split(new[] { ' ', '-', '_', '.', ',' }, StringSplitOptions.RemoveEmptyEntries);
        var vector = new Dictionary<string, double>();
        
        foreach (var term in terms)
        {
            if (vector.ContainsKey(term))
                vector[term]++;
            else
                vector[term] = 1;
        }

        return vector;
    }
}