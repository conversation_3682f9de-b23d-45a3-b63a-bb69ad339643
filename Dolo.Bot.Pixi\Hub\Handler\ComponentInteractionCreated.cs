﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
namespace Dolo.Bot.Pixi.Hub.Handler;

public static class ComponentInteractionCreated
{
    public static async Task InvokeAsync(this ComponentInteractionCreatedEventArgs e)
    {
        if (e.Interaction.Data.ComponentType != DiscordComponentType.Button) return;

        switch (e.Interaction.Data.CustomId)
        {
            case "btn-commands":
                var embed = await HubEmbed.HelpAsync(e.Guild);
                await e.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.ChannelMessageWithSource, new DiscordInteractionResponseBuilder()
                    .AddEmbed(embed.Embeds.First())
                    .AsEphemeral(true));
                return;
            case "btn-changelog":
                var changelog = await HubEmbed.ChangelogAsync(e.Guild);
                await e.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.ChannelMessageWithSource, new DiscordInteractionResponseBuilder()
                    .AddEmbed(changelog.Embeds.First())
                    .AsEphemeral(false));
                return;
            case "feedback":
                await e.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.Modal, new DiscordInteractionResponseBuilder()
                    .WithTitle("Give us Feedback")
                    .WithCustomId("feedback-modal")
                    .AddTextInputComponent(new DiscordTextInputComponent("Feedback", $"{e.Interaction.Data.CustomId}-input", "I want to have ... in the bot", null, true, DiscordTextInputStyle.Paragraph)));
                return;
            case "terms":
                {
                    var user = await Mongo.PixiMembers.GetOneAsync(a => a.Id == e.Interaction.User.Id);
                    if (user is { })
                    {
                        await e.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.UpdateMessage,
                        new DiscordInteractionResponseBuilder()
                            .WithContent("You have already accepted the terms of service. You can now use the bot."));
                        return;
                    }

                    await Mongo.PixiMembers.AddAsync(new PixiMember
                    {
                        Id = e.Interaction.User.Id,
                        Username = e.Interaction.User.Username
                    });

                    await e.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.UpdateMessage,
                    new DiscordInteractionResponseBuilder()
                        .WithContent("You have accepted the terms of service."));
                    return;
                }
        }

        if (e.Interaction.Data.CustomId.StartsWith("report-user-"))
            await e.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.Modal, new DiscordInteractionResponseBuilder()
                .WithTitle("Report a Actor")
                .WithCustomId(e.Interaction.Data.CustomId)
                .AddTextInputComponent(new DiscordTextInputComponent("Please explain a bit more..", $"{e.Interaction.Data.CustomId}-input", "give us more informations", null, true, DiscordTextInputStyle.Paragraph)));
    }
}