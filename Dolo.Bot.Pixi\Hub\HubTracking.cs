﻿using Dolo.Core;
using Dolo.Core.Consola;
using Dolo.Database;
using Dolo.Planet.Entities;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub;

public static class HubTracking
{
    /// <summary>
    ///     Tries to get a member
    /// </summary>
    public static async Task<PixiTrackingMember?> TryGetMemberAsync(string? profileId) => await Mongo.PixiTracking.GetOneAsync(a => a.Actor.ProfileId == profileId);

    /// <summary>
    ///     Tries to get a member
    /// </summary>
    public static async Task<PixiTrackingMember?> TryGetMemberAsync(int id) => await Mongo.PixiTracking.GetOneAsync(a => a.Actor.Id == id);

    /// <summary>
    ///     Add tracking to member
    /// </summary>
    public static async Task AddTrackingAsync(MspActor actor)
        => await TryIt.ThisAsync(async () => {
            await AddMemberAsync(actor);
            await Mongo.PixiTracking.UpdateAsync(Builders<PixiTrackingMember>.Filter.Eq(a => a.Actor.ProfileId, actor.ProfileId),
            Builders<PixiTrackingMember>.Update.AddToSet(a => a.Stats, new(actor)));
        }, a => {
            Consola.Error(a.Message);
        });

    /// <summary>
    ///     Add a new member
    /// </summary>
    private static async Task AddMemberAsync(MspActor actor)
    {
        if (await TryGetMemberAsync(actor.ProfileId) != null)
            return;

        await Mongo.PixiTracking.AddAsync(new PixiTrackingMember(actor));
    }
}