﻿using Dolo.Core.Consola;
namespace Dolo.Bot.Pixi.Hub.Handler;

public static class GuildDownloadCompleted
{
    public static async Task InvokeAsync(this GuildDownloadCompletedEventArgs e)
    {
        Consola.Information($"Loaded {e.Guilds.Count:N0} Guilds");

        // try to get the pluto guild out of the shards
        if (e.Guilds.ContainsKey(708318629112053841))
            Hub.Pluto = e.Guilds.Single(a => a.Value.Id == 708318629112053841)
                .Value;

        // try to get the skid guild out of the shards
        if (e.Guilds.ContainsKey(748570344067039253))
            Hub.Skid = e.Guilds.Single(a => a.Value.Id == 748570344067039253)
                .Value;

        // try to get the moviestar guild out of the shards
        if (e.Guilds.ContainsKey(901861918745190400))
            Hub.MovieStar = e.Guilds.Single(a => a.Value.Id == 901861918745190400)
                .Value;

        await Task.Delay(1);
    }
}