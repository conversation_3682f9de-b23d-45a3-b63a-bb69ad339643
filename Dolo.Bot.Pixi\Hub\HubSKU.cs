﻿using Dolo.Core.Http;
using Newtonsoft.Json;
using System.Text;
namespace Dolo.Bot.Pixi.Hub;

public static class HubSKU
{
    public static string? Token { get; set; }
    /// <summary>
    ///     Interaction response with premium features required
    /// </summary>
    /// <param name="ctx"></param>
    /// <returns></returns>
    public static async Task<bool> NeedPremiumAsync(this SlashCommandContext ctx)
    {
        var token = ctx.Interaction.Token;
        var id = ctx.Interaction.Id;
        var req = await Http.TrySendAsync(a => {
            a.Method = HttpMethod.Post;
            a.Url = $"https://discord.com/api/v10/interactions/{id}/{token}/callback";
            a.Content = new StringContent(JsonConvert.SerializeObject(new
            {
                type = 10
            }), Encoding.UTF8, "application/json");
        });

        return req.IsSuccess;
    }

    /// <summary>
    ///     Gets the user subscriptions
    /// </summary>
    /// <param name="ctx"></param>
    /// <returns></returns>
    public static async Task<List<Entitlement>> GetUserSubscriptionAsync(this CommandContext ctx)
    {
        var userId = ctx.User.Id;
        var applicationId = Hub.Discord.CurrentApplication.Id;
        var req = await Http.TrySendAsync<List<Entitlement>>(a => {
            a.Method = HttpMethod.Get;
            a.Url = $"https://discord.com/api/v9/applications/{applicationId}/entitlements?user_id={userId}";
            a.AuthToken = Token;
            a.AuthType = "Bot";
        });

        return req.Body ?? [];
    }

    public class Entitlement
    {
        public ulong Id { get; set; }
        [JsonProperty("sku_id")]
        public ulong SkuId { get; set; }
        [JsonProperty("application_id")]
        public ulong ApplicationId { get; set; }
        [JsonProperty("user_id")]
        public ulong UserId { get; set; }
        public int Type { get; set; }
        [JsonProperty("deleted")]
        public bool IsDeleted { get; set; }
        [JsonProperty("starts_at")]
        public DateTime? StartsAt { get; set; }
        [JsonProperty("ends_at")]
        public DateTime? EndsAt { get; set; }
        [JsonProperty("guild_id")]
        public ulong GuildId { get; set; }
        [JsonProperty("subscription_id")]
        public ulong SubscriptionPlanId { get; set; }

        public bool IsExpired => EndsAt < DateTime.UtcNow;
    }
}