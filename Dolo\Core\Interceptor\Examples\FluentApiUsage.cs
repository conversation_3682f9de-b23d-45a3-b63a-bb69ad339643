using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor.Services;
using System.Net;

namespace Dolo.Core.Interceptor.Examples;

/// <summary>
/// Examples demonstrating the Fluent API for runtime HTTP request/response modification during breakpoints
/// </summary>
public static class FluentApiUsage
{
    /// <summary>
    /// Example: Modifying request headers and content during a breakpoint
    /// </summary>
    public static async Task ExampleRequestModification()
    {
        var breakpointManager = new BreakpointManager(null!); // Logger would be injected
        
        // Subscribe to breakpoint events
        breakpointManager.BreakpointHit += async (sender, breakpointArgs) =>
        {
            if (breakpointArgs.Type == BreakpointType.Request)
            {
                // FLUENT API: Chain multiple modifications
                breakpointArgs
                    .ModifyHeader("User-Agent", "Dolo-Interceptor/1.0")
                    .ModifyHeader("X-Custom-Header", "Modified-By-Breakpoint")
                    .AddHeader("X-Timestamp", DateTime.UtcNow.ToString("O"))
                    .RemoveHeader("Authorization") // Remove sensitive headers
                    .ModifyContent("{ \"modified\": true, \"timestamp\": \"" + DateTime.UtcNow + "\" }")
                    .ModifyMethod("POST")
                    .ModifyUri("https://api.example.com/v2/modified-endpoint");

                // Automatically resolve to continue with modifications
                breakpointArgs.ResolveBreakpoint(BreakpointAction.Continue);
            }
        };
    }

    /// <summary>
    /// Example: Modifying response status and content during a breakpoint
    /// </summary>
    public static async Task ExampleResponseModification()
    {
        var breakpointManager = new BreakpointManager(null!);
        
        breakpointManager.BreakpointHit += async (sender, breakpointArgs) =>
        {
            if (breakpointArgs.Type == BreakpointType.Response)
            {
                // Check if this is an error response that we want to modify
                if (breakpointArgs.Response?.StatusCode == HttpStatusCode.InternalServerError)
                {
                    // FLUENT API: Convert error to success with custom content
                    breakpointArgs
                        .ModifyStatusCode(HttpStatusCode.OK)
                        .ModifyHeader("Content-Type", "application/json")
                        .ModifyContentAsJson(new { 
                            success = true, 
                            message = "Error intercepted and handled by Dolo",
                            originalError = "500 Internal Server Error",
                            timestamp = DateTime.UtcNow
                        })
                        .AddHeader("X-Intercepted", "true")
                        .AddHeader("X-Original-Status", "500");
                }
                
                breakpointArgs.ResolveBreakpoint(BreakpointAction.Continue);
            }
        };
    }

    /// <summary>
    /// Example: Conditional modifications based on request content
    /// </summary>
    public static async Task ExampleConditionalModification()
    {
        var breakpointManager = new BreakpointManager(null!);
        
        breakpointManager.BreakpointHit += async (sender, breakpointArgs) =>
        {
            if (breakpointArgs.Type == BreakpointType.Request && breakpointArgs.Request != null)
            {
                var content = breakpointArgs.Request.GetContentAsString();
                
                // Modify based on content analysis
                if (content.Contains("password"))
                {
                    // Redact sensitive information
                    var redactedContent = content.Replace("password", "***REDACTED***");
                    breakpointArgs
                        .ModifyContent(redactedContent)
                        .AddHeader("X-Content-Redacted", "true");
                }
                
                // Add authentication if missing
                if (string.IsNullOrEmpty(breakpointArgs.Request.GetHeaderValue("Authorization")))
                {
                    breakpointArgs.ModifyHeader("Authorization", "Bearer auto-injected-token");
                }
                
                breakpointArgs.ResolveBreakpoint(BreakpointAction.Continue);
            }
        };
    }

    /// <summary>
    /// Example: Advanced JSON content modification
    /// </summary>
    public static async Task ExampleJsonModification()
    {
        var breakpointManager = new BreakpointManager(null!);
        
        breakpointManager.BreakpointHit += async (sender, breakpointArgs) =>
        {
            if (breakpointArgs.Type == BreakpointType.Request && 
                breakpointArgs.Request?.ContentType?.Contains("application/json") == true)
            {
                try
                {
                    // Parse existing JSON content
                    var jsonContent = breakpointArgs.Request.GetContentAsJson<Dictionary<string, object>>();
                    
                    if (jsonContent != null)
                    {
                        // Add metadata to JSON
                        jsonContent["_intercepted"] = true;
                        jsonContent["_timestamp"] = DateTime.UtcNow;
                        jsonContent["_connectionId"] = breakpointArgs.ConnectionId;
                        
                        // Apply modified JSON back
                        breakpointArgs.ModifyContentAsJson(jsonContent);
                    }
                }
                catch (Exception ex)
                {
                    // If JSON parsing fails, add error info as header
                    breakpointArgs.AddHeader("X-Json-Parse-Error", ex.Message);
                }
                
                breakpointArgs.ResolveBreakpoint(BreakpointAction.Continue);
            }
        };
    }

    /// <summary>
    /// Example: Reset modifications if needed
    /// </summary>
    public static async Task ExampleResetModifications()
    {
        var breakpointManager = new BreakpointManager(null!);
        
        breakpointManager.BreakpointHit += async (sender, breakpointArgs) =>
        {
            // Make some modifications
            breakpointArgs
                .ModifyHeader("Test-Header", "test-value")
                .ModifyContent("modified content");
            
            // Check if modifications should be applied
            if (SomeCondition())
            {
                // Keep modifications and continue
                breakpointArgs.ResolveBreakpoint(BreakpointAction.Continue);
            }
            else
            {
                // Reset all modifications back to original
                breakpointArgs
                    .ResetModifications()
                    .ResolveBreakpoint(BreakpointAction.Continue);
            }
        };
    }

    /// <summary>
    /// Example: Inspect modifications before applying
    /// </summary>
    public static async Task ExampleInspectModifications()
    {
        var breakpointManager = new BreakpointManager(null!);
        
        breakpointManager.BreakpointHit += async (sender, breakpointArgs) =>
        {
            // Make modifications
            breakpointArgs
                .ModifyHeader("X-Modified", "true")
                .ModifyContent("new content");
            
            // Check if any modifications were made
            if (breakpointArgs.HasModifications)
            {
                // Get the modified request/response to inspect
                var modifiedRequest = breakpointArgs.GetModifiedRequest();
                var modifiedResponse = breakpointArgs.GetModifiedResponse();
                
                // Log or validate modifications before applying
                Console.WriteLine($"Modifications detected for {breakpointArgs.BreakpointId}");
                
                // Apply modifications by resolving the breakpoint
                breakpointArgs.ResolveBreakpoint(BreakpointAction.Continue);
            }
            else
            {
                // No modifications, continue normally
                breakpointArgs.ResolveBreakpoint(BreakpointAction.Continue);
            }
        };
    }

    private static bool SomeCondition() => DateTime.Now.Millisecond % 2 == 0;
}

/// <summary>
/// Integration example showing how to use the fluent API in a real interceptor setup
/// </summary>
public class FluentApiIntegrationExample
{
    private readonly BreakpointManager _breakpointManager;

    public FluentApiIntegrationExample(BreakpointManager breakpointManager)
    {
        _breakpointManager = breakpointManager;
        
        // Subscribe to breakpoint events for runtime modification
        _breakpointManager.BreakpointHit += OnBreakpointHit;
    }

    private async Task OnBreakpointHit(BreakpointManager sender, BreakpointHitEventArgs e)
    {
        // Example: Automatic API key injection for specific domains
        if (e.Type == BreakpointType.Request && 
            e.Hostname.Contains("api.moviestarplanet.com"))
        {
            e.ModifyHeader("X-API-Key", "auto-injected-key")
             .AddHeader("X-Intercepted-By", "Dolo")
             .ResolveBreakpoint(BreakpointAction.Continue);
            return;
        }

        // Example: Error response transformation
        if (e.Type == BreakpointType.Response && 
            e.Response?.StatusCode >= HttpStatusCode.BadRequest)
        {
            e.ModifyStatusCode(HttpStatusCode.OK)
             .ModifyContentAsJson(new { 
                 error = "handled", 
                 originalStatus = (int)e.Response.StatusCode 
             })
             .ResolveBreakpoint(BreakpointAction.Continue);
            return;
        }

        // Default: Continue without modifications
        e.ResolveBreakpoint(BreakpointAction.Continue);
    }
}
