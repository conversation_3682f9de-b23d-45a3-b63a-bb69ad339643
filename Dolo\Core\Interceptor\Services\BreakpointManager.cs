using System.Collections.Concurrent;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// Manages breakpoints with queue-based processing ensuring FIFO order and thread safety
/// </summary>
public sealed class BreakpointManager : IDisposable
{
    private readonly BreakpointConfig _config;
    private readonly ILogger<BreakpointManager> _logger;
    private readonly SemaphoreSlim _breakpointSemaphore;
    private readonly ConcurrentDictionary<string, BreakpointHitEventArgs> _activeBreakpoints = new();
    private readonly Queue<BreakpointHitEventArgs> _breakpointQueue = new();
    private readonly object _queueLock = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly Task _processingTask;
    private bool _disposed;

    public event AsyncEventHandler<BreakpointManager, BreakpointHitEventArgs>? BreakpointHit;

    public BreakpointManager(BreakpointConfig config, ILogger<BreakpointManager> logger)
    {
        _config = config;
        _logger = logger;
        _breakpointSemaphore = new SemaphoreSlim(_config.MaxConcurrentBreakpoints, _config.MaxConcurrentBreakpoints);
        _processingTask = Task.Run(ProcessBreakpointQueueAsync, _cancellationTokenSource.Token);
        
        _logger.LogInformation("🔴 BreakpointManager initialized with max concurrent breakpoints: {MaxConcurrent}", 
            _config.MaxConcurrentBreakpoints);
    }

    /// <summary>
    /// Checks if a request should trigger a breakpoint and processes it if needed
    /// Returns the breakpoint action result, or null if no breakpoint was triggered
    /// </summary>
    public async Task<BreakpointAction?> CheckRequestBreakpointAsync(
        string transactionId,
        string connectionId,
        string hostname,
        int port,
        string protocol,
        IHttpInterceptedRequestMessage request)
    {
        if (_disposed || !_config.EnableRequestBreakpoints)
            return null;

        var url = $"{protocol}://{hostname}:{port}{request.Uri?.PathAndQuery}";

        if (!_config.ShouldBreakpoint(url, BreakpointType.Request))
            return null;

        var breakpointId = $"req-{transactionId}-{DateTime.UtcNow.Ticks}";
        var breakpointArgs = new BreakpointHitEventArgs(
            breakpointId,
            BreakpointType.Request,
            transactionId,
            connectionId,
            hostname,
            port,
            protocol,
            request: request);

        return await ProcessBreakpointAsync(breakpointArgs).ConfigureAwait(false);
    }

    /// <summary>
    /// Checks if a response should trigger a breakpoint and processes it if needed
    /// Returns the breakpoint action result, or null if no breakpoint was triggered
    /// </summary>
    public async Task<BreakpointAction?> CheckResponseBreakpointAsync(
        string transactionId,
        string connectionId,
        string hostname,
        int port,
        string protocol,
        IHttpInterceptedResponseMessage response)
    {
        if (_disposed || !_config.EnableResponseBreakpoints)
            return null;

        var url = $"{protocol}://{hostname}:{port}{response.Uri?.PathAndQuery}";

        if (!_config.ShouldBreakpoint(url, BreakpointType.Response))
            return null;

        var breakpointId = $"res-{transactionId}-{DateTime.UtcNow.Ticks}";
        var breakpointArgs = new BreakpointHitEventArgs(
            breakpointId,
            BreakpointType.Response,
            transactionId,
            connectionId,
            hostname,
            port,
            protocol,
            response: response);

        return await ProcessBreakpointAsync(breakpointArgs).ConfigureAwait(false);
    }

    /// <summary>
    /// Processes a breakpoint by adding it to the queue and waiting for resolution
    /// </summary>
    private async Task<BreakpointAction> ProcessBreakpointAsync(BreakpointHitEventArgs breakpointArgs)
    {
        try
        {
            lock (_queueLock)
            {
                _breakpointQueue.Enqueue(breakpointArgs);
                _logger.LogDebug("🔴 Breakpoint {BreakpointId} queued for {Type} on {Url}",
                    breakpointArgs.BreakpointId, breakpointArgs.Type, breakpointArgs.Url);
            }

            // Wait indefinitely for the breakpoint to be resolved by user input
            var action = await breakpointArgs.WaitForResolutionAsync().ConfigureAwait(false);

            _logger.LogInformation("🔴 Breakpoint {BreakpointId} resolved with action: {Action}",
                breakpointArgs.BreakpointId, action);

            return action;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error processing breakpoint {BreakpointId}: {Error}",
                breakpointArgs.BreakpointId, ex.Message);
            return BreakpointAction.Continue; // Continue on error
        }
    }

    /// <summary>
    /// Background task that processes the breakpoint queue
    /// </summary>
    private async Task ProcessBreakpointQueueAsync()
    {
        while (!_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                BreakpointHitEventArgs? breakpointArgs = null;
                
                lock (_queueLock)
                {
                    if (_breakpointQueue.Count > 0)
                    {
                        breakpointArgs = _breakpointQueue.Dequeue();
                    }
                }

                if (breakpointArgs != null)
                {
                    await ProcessSingleBreakpointAsync(breakpointArgs).ConfigureAwait(false);
                }
                else
                {
                    await Task.Delay(50, _cancellationTokenSource.Token).ConfigureAwait(false);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error in breakpoint queue processing: {Error}", ex.Message);
                await Task.Delay(1000, _cancellationTokenSource.Token).ConfigureAwait(false);
            }
        }
    }

    /// <summary>
    /// Processes a single breakpoint with semaphore control
    /// </summary>
    private async Task ProcessSingleBreakpointAsync(BreakpointHitEventArgs breakpointArgs)
    {
        await _breakpointSemaphore.WaitAsync(_cancellationTokenSource.Token).ConfigureAwait(false);

        try
        {
            _activeBreakpoints[breakpointArgs.BreakpointId] = breakpointArgs;

            _logger.LogInformation("🔴 Processing breakpoint {BreakpointId} for {Type} on {Url}",
                breakpointArgs.BreakpointId, breakpointArgs.Type, breakpointArgs.Url);

            // Fire the breakpoint event and wait for user input
            if (BreakpointHit != null)
            {
                _logger.LogInformation("🔴 Firing BreakpointHit event for {BreakpointId}", breakpointArgs.BreakpointId);
                await BreakpointHit.InvokeAsync(this, breakpointArgs).ConfigureAwait(false);
                _logger.LogInformation("🔴 BreakpointHit event completed for {BreakpointId}", breakpointArgs.BreakpointId);
            }
            else
            {
                _logger.LogWarning("🔴 No BreakpointHit event handler registered for {BreakpointId}", breakpointArgs.BreakpointId);
            }

            _logger.LogInformation("🔴 Waiting for user input for breakpoint {BreakpointId}...", breakpointArgs.BreakpointId);
            // Wait indefinitely for user input - no timeout
            await breakpointArgs.ActionSource.Task.ConfigureAwait(false);
            _logger.LogInformation("🔴 User input received for breakpoint {BreakpointId}", breakpointArgs.BreakpointId);

            _logger.LogInformation("🔴 Breakpoint {BreakpointId} completed by user input",
                breakpointArgs.BreakpointId);
        }
        finally
        {
            _activeBreakpoints.TryRemove(breakpointArgs.BreakpointId, out _);
            _breakpointSemaphore.Release();
        }
    }

    /// <summary>
    /// Resolves a breakpoint with the specified action
    /// </summary>
    public bool ResolveBreakpoint(string breakpointId, BreakpointAction action)
    {
        if (_activeBreakpoints.TryGetValue(breakpointId, out var breakpointArgs))
        {
            breakpointArgs.ResolveBreakpoint(action);
            return true;
        }
        return false;
    }

    /// <summary>
    /// Gets all active breakpoints
    /// </summary>
    public IReadOnlyDictionary<string, BreakpointHitEventArgs> GetActiveBreakpoints()
    {
        return _activeBreakpoints.AsReadOnly();
    }

    /// <summary>
    /// Gets the current queue size
    /// </summary>
    public int QueueSize
    {
        get
        {
            lock (_queueLock)
            {
                return _breakpointQueue.Count;
            }
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _cancellationTokenSource.Cancel();
        
        try
        {
            _processingTask.Wait(TimeSpan.FromSeconds(5));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ Error waiting for breakpoint processing task to complete");
        }

        // Resolve any remaining breakpoints
        foreach (var breakpoint in _activeBreakpoints.Values)
        {
            breakpoint.ResolveBreakpoint(BreakpointAction.Continue);
        }

        _activeBreakpoints.Clear();
        _breakpointSemaphore.Dispose();
        _cancellationTokenSource.Dispose();
        
        _logger.LogInformation("🔴 BreakpointManager disposed");
    }
}
