using System.Collections.Concurrent;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// Manages breakpoints with per-connection isolation ensuring FIFO order and thread safety
/// FIXED: Replaced global semaphore with per-connection breakpoint queues for parallel processing
/// </summary>
public sealed class BreakpointManager : IDisposable
{
    private readonly BreakpointConfig _config;
    private readonly ILogger<BreakpointManager> _logger;

    // Per-connection breakpoint isolation - CRITICAL FIX
    private readonly ConcurrentDictionary<string, ConnectionBreakpointContext> _connectionContexts = new();
    private readonly ConcurrentDictionary<string, BreakpointHitEventArgs> _activeBreakpoints = new();

    // Global processing coordination
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly Task _processingTask;
    private bool _disposed;

    /// <summary>
    /// Per-connection breakpoint context for isolated processing
    /// </summary>
    private sealed class ConnectionBreakpointContext
    {
        public readonly Queue<BreakpointHitEventArgs> BreakpointQueue = new();
        public readonly SemaphoreSlim ProcessingSemaphore;
        public readonly object QueueLock = new();
        public readonly string ConnectionId;
        public int ActiveBreakpoints;

        public ConnectionBreakpointContext(string connectionId, int maxConcurrentBreakpoints)
        {
            ConnectionId = connectionId;
            ProcessingSemaphore = new SemaphoreSlim(maxConcurrentBreakpoints, maxConcurrentBreakpoints);
        }

        public void Dispose()
        {
            ProcessingSemaphore.Dispose();
        }
    }

    public event AsyncEventHandler<BreakpointManager, BreakpointHitEventArgs>? BreakpointHit;

    public BreakpointManager(BreakpointConfig config, ILogger<BreakpointManager> logger)
    {
        _config = config;
        _logger = logger;

        // Start global processing task for all connections
        _processingTask = Task.Run(ProcessAllConnectionBreakpointsAsync, _cancellationTokenSource.Token);

        _logger.LogInformation("🔴 BreakpointManager initialized with per-connection isolation (max {MaxConcurrent} per connection)",
            _config.MaxConcurrentBreakpoints);
    }

    /// <summary>
    /// Gets or creates a connection-specific breakpoint context
    /// </summary>
    private ConnectionBreakpointContext GetOrCreateConnectionContext(string connectionId)
    {
        return _connectionContexts.GetOrAdd(connectionId,
            id => new ConnectionBreakpointContext(id, _config.MaxConcurrentBreakpoints));
    }

    /// <summary>
    /// Removes a connection context when the connection is closed
    /// </summary>
    public void RemoveConnectionContext(string connectionId)
    {
        if (_connectionContexts.TryRemove(connectionId, out var context))
        {
            // Cancel any pending breakpoints for this connection
            lock (context.QueueLock)
            {
                while (context.BreakpointQueue.Count > 0)
                {
                    var breakpoint = context.BreakpointQueue.Dequeue();
                    breakpoint.ResolveBreakpoint(BreakpointAction.Continue);
                }
            }

            context.Dispose();
            _logger.LogDebug("🔴 Removed breakpoint context for connection {ConnectionId}", connectionId);
        }
    }

    /// <summary>
    /// Checks if a request should trigger a breakpoint and processes it if needed
    /// Returns the breakpoint action result, or null if no breakpoint was triggered
    /// </summary>
    public async Task<BreakpointAction?> CheckRequestBreakpointAsync(
        string transactionId,
        string connectionId,
        string hostname,
        int port,
        string protocol,
        IHttpInterceptedRequestMessage request)
    {
        if (_disposed || !_config.EnableRequestBreakpoints)
            return null;

        var url = $"{protocol}://{hostname}:{port}{request.Uri?.PathAndQuery}";

        if (!_config.ShouldBreakpoint(url, BreakpointType.Request))
            return null;

        var breakpointId = $"req-{transactionId}-{DateTime.UtcNow.Ticks}";
        var breakpointArgs = new BreakpointHitEventArgs(
            breakpointId,
            BreakpointType.Request,
            transactionId,
            connectionId,
            hostname,
            port,
            protocol,
            request: request);

        return await ProcessBreakpointAsync(breakpointArgs).ConfigureAwait(false);
    }

    /// <summary>
    /// Checks if a response should trigger a breakpoint and processes it if needed
    /// Returns the breakpoint action result, or null if no breakpoint was triggered
    /// </summary>
    public async Task<BreakpointAction?> CheckResponseBreakpointAsync(
        string transactionId,
        string connectionId,
        string hostname,
        int port,
        string protocol,
        IHttpInterceptedResponseMessage response)
    {
        if (_disposed || !_config.EnableResponseBreakpoints)
            return null;

        var url = $"{protocol}://{hostname}:{port}{response.Uri?.PathAndQuery}";

        if (!_config.ShouldBreakpoint(url, BreakpointType.Response))
            return null;

        var breakpointId = $"res-{transactionId}-{DateTime.UtcNow.Ticks}";
        var breakpointArgs = new BreakpointHitEventArgs(
            breakpointId,
            BreakpointType.Response,
            transactionId,
            connectionId,
            hostname,
            port,
            protocol,
            response: response);

        return await ProcessBreakpointAsync(breakpointArgs).ConfigureAwait(false);
    }

    /// <summary>
    /// Processes a breakpoint using per-connection isolation - CRITICAL FIX
    /// </summary>
    private async Task<BreakpointAction> ProcessBreakpointAsync(BreakpointHitEventArgs breakpointArgs)
    {
        try
        {
            var connectionContext = GetOrCreateConnectionContext(breakpointArgs.ConnectionId);

            // Add to connection-specific queue
            lock (connectionContext.QueueLock)
            {
                connectionContext.BreakpointQueue.Enqueue(breakpointArgs);
                _logger.LogDebug("🔴 Breakpoint {BreakpointId} queued for {Type} on connection {ConnectionId}",
                    breakpointArgs.BreakpointId, breakpointArgs.Type, breakpointArgs.ConnectionId);
            }

            // Wait indefinitely for the breakpoint to be resolved by user input
            var action = await breakpointArgs.WaitForResolutionAsync().ConfigureAwait(false);

            _logger.LogInformation("🔴 Breakpoint {BreakpointId} resolved with action: {Action} (connection: {ConnectionId})",
                breakpointArgs.BreakpointId, action, breakpointArgs.ConnectionId);

            return action;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error processing breakpoint {BreakpointId}: {Error}",
                breakpointArgs.BreakpointId, ex.Message);
            return BreakpointAction.Continue; // Continue on error
        }
    }

    /// <summary>
    /// Background task that processes breakpoint queues for all connections - REDESIGNED
    /// </summary>
    private async Task ProcessAllConnectionBreakpointsAsync()
    {
        while (!_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                var processedAny = false;

                // Process breakpoints for each connection independently
                foreach (var kvp in _connectionContexts.ToArray())
                {
                    var connectionId = kvp.Key;
                    var context = kvp.Value;

                    // Try to process one breakpoint from this connection's queue
                    if (await TryProcessConnectionBreakpointAsync(context).ConfigureAwait(false))
                    {
                        processedAny = true;
                    }
                }

                if (!processedAny)
                {
                    await Task.Delay(50, _cancellationTokenSource.Token).ConfigureAwait(false);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error in connection breakpoint processing: {Error}", ex.Message);
                await Task.Delay(1000, _cancellationTokenSource.Token).ConfigureAwait(false);
            }
        }
    }

    /// <summary>
    /// Tries to process one breakpoint from a connection's queue
    /// </summary>
    private async Task<bool> TryProcessConnectionBreakpointAsync(ConnectionBreakpointContext context)
    {
        BreakpointHitEventArgs? breakpointArgs = null;

        lock (context.QueueLock)
        {
            if (context.BreakpointQueue.Count > 0)
            {
                breakpointArgs = context.BreakpointQueue.Dequeue();
            }
        }

        if (breakpointArgs != null)
        {
            // Process this breakpoint in the background for this connection
            _ = Task.Run(async () => await ProcessSingleConnectionBreakpointAsync(context, breakpointArgs).ConfigureAwait(false));
            return true;
        }

        return false;
    }

    /// <summary>
    /// Processes a single breakpoint with per-connection semaphore control - CRITICAL FIX
    /// </summary>
    private async Task ProcessSingleConnectionBreakpointAsync(ConnectionBreakpointContext context, BreakpointHitEventArgs breakpointArgs)
    {
        await context.ProcessingSemaphore.WaitAsync(_cancellationTokenSource.Token).ConfigureAwait(false);

        try
        {
            Interlocked.Increment(ref context.ActiveBreakpoints);
            _activeBreakpoints[breakpointArgs.BreakpointId] = breakpointArgs;

            _logger.LogInformation("🔴 Processing breakpoint {BreakpointId} for {Type} on connection {ConnectionId} (active: {Active})",
                breakpointArgs.BreakpointId, breakpointArgs.Type, context.ConnectionId, context.ActiveBreakpoints);

            // Fire the breakpoint event and wait for user input
            if (BreakpointHit != null)
            {
                _logger.LogInformation("🔴 Firing BreakpointHit event for {BreakpointId}", breakpointArgs.BreakpointId);
                await BreakpointHit.InvokeAsync(this, breakpointArgs).ConfigureAwait(false);
                _logger.LogInformation("🔴 BreakpointHit event completed for {BreakpointId}", breakpointArgs.BreakpointId);
            }
            else
            {
                _logger.LogWarning("🔴 No BreakpointHit event handler registered for {BreakpointId}", breakpointArgs.BreakpointId);
            }

            _logger.LogInformation("🔴 Waiting for user input for breakpoint {BreakpointId}...", breakpointArgs.BreakpointId);
            // Wait indefinitely for user input - no timeout
            await breakpointArgs.ActionSource.Task.ConfigureAwait(false);
            _logger.LogInformation("🔴 User input received for breakpoint {BreakpointId}", breakpointArgs.BreakpointId);

            _logger.LogInformation("🔴 Breakpoint {BreakpointId} completed by user input (connection: {ConnectionId})",
                breakpointArgs.BreakpointId, context.ConnectionId);
        }
        finally
        {
            _activeBreakpoints.TryRemove(breakpointArgs.BreakpointId, out _);
            Interlocked.Decrement(ref context.ActiveBreakpoints);
            context.ProcessingSemaphore.Release();
        }
    }

    /// <summary>
    /// Resolves a breakpoint with the specified action
    /// </summary>
    public bool ResolveBreakpoint(string breakpointId, BreakpointAction action)
    {
        if (_activeBreakpoints.TryGetValue(breakpointId, out var breakpointArgs))
        {
            breakpointArgs.ResolveBreakpoint(action);
            return true;
        }
        return false;
    }

    /// <summary>
    /// Gets all active breakpoints
    /// </summary>
    public IReadOnlyDictionary<string, BreakpointHitEventArgs> GetActiveBreakpoints()
    {
        return _activeBreakpoints.AsReadOnly();
    }

    /// <summary>
    /// Gets the current total queue size across all connections
    /// </summary>
    public int QueueSize
    {
        get
        {
            var totalSize = 0;
            foreach (var context in _connectionContexts.Values)
            {
                lock (context.QueueLock)
                {
                    totalSize += context.BreakpointQueue.Count;
                }
            }
            return totalSize;
        }
    }

    /// <summary>
    /// Gets statistics for all connections
    /// </summary>
    public (int TotalConnections, int TotalQueued, int TotalActive) GetStatistics()
    {
        var totalQueued = 0;
        var totalActive = 0;

        foreach (var context in _connectionContexts.Values)
        {
            lock (context.QueueLock)
            {
                totalQueued += context.BreakpointQueue.Count;
            }
            totalActive += context.ActiveBreakpoints;
        }

        return (_connectionContexts.Count, totalQueued, totalActive);
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _cancellationTokenSource.Cancel();

        try
        {
            _processingTask.Wait(TimeSpan.FromSeconds(5));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ Error waiting for breakpoint processing task to complete");
        }

        // Resolve any remaining breakpoints
        foreach (var breakpoint in _activeBreakpoints.Values)
        {
            breakpoint.ResolveBreakpoint(BreakpointAction.Continue);
        }

        _activeBreakpoints.Clear();

        // Dispose all connection contexts
        foreach (var context in _connectionContexts.Values)
        {
            context.Dispose();
        }
        _connectionContexts.Clear();

        _cancellationTokenSource.Dispose();

        _logger.LogInformation("🔴 BreakpointManager disposed");
    }
}
