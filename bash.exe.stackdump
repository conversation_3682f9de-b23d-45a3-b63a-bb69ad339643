Stack trace:
Frame         Function      Args
0007FFFF9B70  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9B70, 0007FFFF8A70) msys-2.0.dll+0x1FE8E
0007FFFF9B70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E48) msys-2.0.dll+0x67F9
0007FFFF9B70  000210046832 (000210286019, 0007FFFF9A28, 0007FFFF9B70, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B70  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9B70  000210068E24 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9E50  00021006A225 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE894E0000 ntdll.dll
7FFE87F90000 KERNEL32.DLL
7FFE86A20000 KERNELBASE.dll
7FFE83210000 apphelp.dll
7FFE888A0000 USER32.dll
7FFE86930000 win32u.dll
7FFE88200000 GDI32.dll
7FFE86610000 gdi32full.dll
7FFE86E10000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFE86750000 ucrtbase.dll
7FFE873D0000 advapi32.dll
7FFE88F10000 msvcrt.dll
7FFE890E0000 sechost.dll
7FFE88FC0000 RPCRT4.dll
7FFE85C10000 CRYPTBASE.DLL
7FFE86EC0000 bcryptPrimitives.dll
7FFE89190000 IMM32.DLL
