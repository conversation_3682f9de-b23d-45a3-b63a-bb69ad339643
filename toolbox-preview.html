<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pluto Toolbox Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=Signika:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'signika': ['Signika', 'sans-serif'],
                    },
                    keyframes: {
                        'come-in': {
                            '0%': { opacity: '0', transform: 'translateY(-10px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        'shine': {
                            '0%': { transform: 'translateX(-100%) skewX(-12deg)' },
                            '100%': { transform: 'translateX(200%) skewX(-12deg)' },
                        },
                    },
                    animation: {
                        'come-in': 'come-in 0.5s ease-in-out',
                        'come-in-0.6': 'come-in 0.6s ease-in-out',
                        'come-in-0.7': 'come-in 0.7s ease-in-out',
                        'come-in-0.8': 'come-in 0.8s ease-in-out',
                        'come-in-0.9': 'come-in 0.9s ease-in-out',
                        'shine': 'shine 3s ease-in-out infinite 2s',
                    },
                },
            },
        }
    </script>
    <style>
        .shine-effect::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }
        .shine-effect:hover::before {
            left: 100%;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen">
    <!-- Header Navigation (simplified) -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
        <div class="max-w-6xl mx-auto flex items-center justify-between">
            <div class="flex items-center gap-3">
                <img src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/logo.png" alt="Pluto Logo" class="h-8 w-8">
                <span class="text-xl font-bold">Pluto Tools</span>
            </div>
            <div class="flex items-center gap-4">
                <button class="px-4 py-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">Personal</button>
                <button class="px-4 py-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">Shop</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-[60vh] flex items-center justify-center px-4 py-12">
        <div class="w-full max-w-4xl">
            <div class="flex flex-col md:flex-row gap-12 items-center">
                <!-- Left side - Content -->
                <div class="flex-1 text-center md:text-left">
                    <div class="relative mb-6">
                        <h1 class="text-5xl font-black font-['Inter'] text-slate-800 leading-tight">
                            Pluto <span class="text-blue-600">Toolbox</span>
                        </h1>
                    </div>

                    <p class="text-lg text-slate-600 font-['Signika'] mb-8">
                        All your MovieStarPlanet tools in one place. Simple, secure, and seamlessly
                        integrated.
                    </p>

                    <!-- Download Button -->
                    <button class="relative shine-effect inline-flex items-center gap-3 px-8 py-4 
                                bg-gradient-to-r from-blue-600 to-blue-700     
                                text-white rounded-xl
                                shadow-lg shadow-blue-500/25
                                transition-all duration-200 transform hover:scale-[1.02] active:scale-95
                                border-2 border-transparent overflow-hidden mb-6">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="font-semibold">Download Now</span>
                        <span class="text-xs bg-white/20 px-2 py-1 rounded-full">v2025.1</span>
                    </button>

                    <!-- Features -->
                    <div class="flex flex-wrap gap-4 text-sm text-slate-600">
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span>Ready to Install</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span>Secure & Safe</span>
                        </div>
                    </div>
                </div>

                <!-- Right side - Preview -->
                <div class="relative w-full md:w-[380px]">
                    <!-- Main toolbox preview container -->
                    <div class="relative">
                        <!-- Toolbox screenshot - smaller with shine effect -->
                        <div class="relative overflow-hidden rounded-2xl">
                            <img src="https://raw.githubusercontent.com/cydolo/assets/refs/heads/main/pluto/toolbox-app.png"
                                 alt="Pluto Toolbox"
                                 class="w-full h-auto object-cover rounded-2xl shadow-2xl scale-90 origin-center">

                            <!-- Shine effect overlay -->
                            <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full animate-shine"></div>
                        </div>

                        <!-- Cards positioned as shown in your drawing - improved styling -->
                        <!-- Top right card - next to "System ready" -->
                        <div class="absolute top-16 -right-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-xl px-5 py-3 border border-blue-200/50 transform hover:scale-105 transition-all duration-300">
                            <p class="text-sm font-semibold text-blue-800">Fast Setup</p>
                        </div>

                        <!-- Bottom left card - in the empty space -->
                        <div class="absolute bottom-20 -left-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-xl px-5 py-3 border border-green-200/50 transform hover:scale-105 transition-all duration-300">
                            <p class="text-sm font-semibold text-green-800">Good Overview</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
