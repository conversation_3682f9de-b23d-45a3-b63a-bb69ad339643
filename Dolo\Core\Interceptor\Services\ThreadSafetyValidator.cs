using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// THREAD SAFETY VALIDATION service for detecting race conditions and thread safety violations
/// Monitors shared state access patterns and reports potential issues
/// </summary>
public sealed class ThreadSafetyValidator : IDisposable
{
    private readonly ILogger<ThreadSafetyValidator> _logger;
    private readonly Timer _reportingTimer;
    private readonly object _validationLock = new();
    private bool _disposed;

    // THREAD ACCESS TRACKING - Monitor concurrent access patterns
    private readonly ConcurrentDictionary<string, ThreadAccessInfo> _accessPatterns = new();
    private readonly ConcurrentDictionary<int, string> _threadOperations = new();
    private readonly ConcurrentQueue<ThreadSafetyViolation> _violations = new();

    // PERFORMANCE IMPACT TRACKING
    private long _totalValidations;
    private long _totalViolations;
    private readonly TimeSpan _reportingInterval = TimeSpan.FromMinutes(5);

    public ThreadSafetyValidator(ILogger<ThreadSafetyValidator> logger)
    {
        _logger = logger;
        _reportingTimer = new Timer(ReportThreadSafetyStatus, null, _reportingInterval, _reportingInterval);
        
        _logger.LogInformation("🔒 Thread safety validator initialized");
    }

    /// <summary>
    /// VALIDATION: Records access to shared state and detects potential race conditions
    /// </summary>
    public void ValidateSharedAccess(string resourceName, string operation, bool isWrite = false)
    {
        if (_disposed) return;

        var threadId = Thread.CurrentThread.ManagedThreadId;
        var timestamp = DateTime.UtcNow;
        
        Interlocked.Increment(ref _totalValidations);

        // Track thread operation
        _threadOperations[threadId] = $"{resourceName}:{operation}";

        // Get or create access info for this resource
        var accessInfo = _accessPatterns.GetOrAdd(resourceName, _ => new ThreadAccessInfo(resourceName));

        // Check for potential race conditions
        var violation = accessInfo.RecordAccess(threadId, operation, isWrite, timestamp);
        if (violation != null)
        {
            _violations.Enqueue(violation);
            Interlocked.Increment(ref _totalViolations);
            
            _logger.LogWarning("⚠️ Thread safety violation detected: {Violation}", violation);
        }
    }

    /// <summary>
    /// VALIDATION: Validates that a lock is held when accessing shared state
    /// </summary>
    public void ValidateLockHeld(string resourceName, object lockObject)
    {
        if (_disposed) return;

        var threadId = Thread.CurrentThread.ManagedThreadId;
        
        // Check if current thread holds the lock
        var lockHeld = Monitor.IsEntered(lockObject);
        if (!lockHeld)
        {
            var violation = new ThreadSafetyViolation
            {
                ResourceName = resourceName,
                ViolationType = ViolationType.LockNotHeld,
                ThreadId = threadId,
                Timestamp = DateTime.UtcNow,
                Details = $"Thread {threadId} accessed {resourceName} without holding required lock"
            };
            
            _violations.Enqueue(violation);
            Interlocked.Increment(ref _totalViolations);
            
            _logger.LogError("❌ CRITICAL: Lock not held for {ResourceName} on thread {ThreadId}", resourceName, threadId);
        }
    }

    /// <summary>
    /// VALIDATION: Validates atomic operation usage for counters
    /// </summary>
    public void ValidateAtomicOperation(string counterName, long expectedValue, long actualValue)
    {
        if (_disposed) return;

        if (expectedValue != actualValue)
        {
            var violation = new ThreadSafetyViolation
            {
                ResourceName = counterName,
                ViolationType = ViolationType.AtomicViolation,
                ThreadId = Thread.CurrentThread.ManagedThreadId,
                Timestamp = DateTime.UtcNow,
                Details = $"Atomic counter {counterName} inconsistency: expected {expectedValue}, actual {actualValue}"
            };
            
            _violations.Enqueue(violation);
            Interlocked.Increment(ref _totalViolations);
            
            _logger.LogError("❌ ATOMIC VIOLATION: {CounterName} inconsistency detected", counterName);
        }
    }

    /// <summary>
    /// Gets current thread safety statistics
    /// </summary>
    public ThreadSafetyStatistics GetStatistics()
    {
        var activeThreads = _threadOperations.Count;
        var monitoredResources = _accessPatterns.Count;
        var recentViolations = _violations.Count;
        
        return new ThreadSafetyStatistics
        {
            TotalValidations = Interlocked.Read(ref _totalValidations),
            TotalViolations = Interlocked.Read(ref _totalViolations),
            ActiveThreads = activeThreads,
            MonitoredResources = monitoredResources,
            RecentViolations = recentViolations,
            ViolationRate = _totalValidations > 0 ? (double)_totalViolations / _totalValidations : 0
        };
    }

    /// <summary>
    /// Gets detailed access patterns for analysis
    /// </summary>
    public Dictionary<string, ThreadAccessSummary> GetAccessPatterns()
    {
        var result = new Dictionary<string, ThreadAccessSummary>();
        
        foreach (var (resourceName, accessInfo) in _accessPatterns)
        {
            result[resourceName] = accessInfo.GetSummary();
        }
        
        return result;
    }

    /// <summary>
    /// Gets recent thread safety violations
    /// </summary>
    public List<ThreadSafetyViolation> GetRecentViolations(int maxCount = 50)
    {
        var violations = new List<ThreadSafetyViolation>();
        var count = 0;
        
        while (count < maxCount && _violations.TryDequeue(out var violation))
        {
            violations.Add(violation);
            count++;
        }
        
        return violations;
    }

    private void ReportThreadSafetyStatus(object? state)
    {
        if (_disposed) return;

        try
        {
            var stats = GetStatistics();
            
            _logger.LogInformation("🔒 Thread Safety Report: {Validations} validations, {Violations} violations ({Rate:P2} rate), {Threads} active threads, {Resources} monitored resources",
                stats.TotalValidations, stats.TotalViolations, stats.ViolationRate, stats.ActiveThreads, stats.MonitoredResources);

            // Report critical violations
            if (stats.ViolationRate > 0.01) // More than 1% violation rate
            {
                _logger.LogWarning("⚠️ HIGH VIOLATION RATE: {Rate:P2} - Review thread safety implementation", stats.ViolationRate);
            }

            // Clean up old thread operations
            var currentThreadIds = Process.GetCurrentProcess().Threads.Cast<ProcessThread>().Select(t => t.Id).ToHashSet();
            var staleThreads = _threadOperations.Keys.Where(id => !currentThreadIds.Contains(id)).ToList();
            
            foreach (var staleThreadId in staleThreads)
            {
                _threadOperations.TryRemove(staleThreadId, out _);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error in thread safety reporting");
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _reportingTimer?.Dispose();
        
        // Final report
        var stats = GetStatistics();
        _logger.LogInformation("🔒 Final thread safety report: {Validations} total validations, {Violations} total violations",
            stats.TotalValidations, stats.TotalViolations);

        if (stats.TotalViolations > 0)
        {
            _logger.LogWarning("⚠️ Thread safety violations detected during session - review implementation");
        }
    }
}

/// <summary>
/// Thread access information for a specific resource
/// </summary>
public sealed class ThreadAccessInfo
{
    private readonly string _resourceName;
    private readonly ConcurrentDictionary<int, List<AccessRecord>> _threadAccess = new();
    private readonly object _accessLock = new();

    public ThreadAccessInfo(string resourceName)
    {
        _resourceName = resourceName;
    }

    public ThreadSafetyViolation? RecordAccess(int threadId, string operation, bool isWrite, DateTime timestamp)
    {
        lock (_accessLock)
        {
            var accessList = _threadAccess.GetOrAdd(threadId, _ => new List<AccessRecord>());
            var record = new AccessRecord(operation, isWrite, timestamp);
            accessList.Add(record);

            // Keep only recent access records (last 100 per thread)
            if (accessList.Count > 100)
            {
                accessList.RemoveRange(0, accessList.Count - 100);
            }

            // Check for potential race conditions
            return DetectRaceCondition(threadId, record);
        }
    }

    private ThreadSafetyViolation? DetectRaceCondition(int currentThreadId, AccessRecord currentAccess)
    {
        if (!currentAccess.IsWrite) return null;

        var recentWindow = DateTime.UtcNow.Subtract(TimeSpan.FromMilliseconds(100));
        
        // Check for concurrent write operations from different threads
        foreach (var (threadId, accessList) in _threadAccess)
        {
            if (threadId == currentThreadId) continue;

            var recentWrites = accessList.Where(a => a.IsWrite && a.Timestamp > recentWindow).ToList();
            if (recentWrites.Any())
            {
                return new ThreadSafetyViolation
                {
                    ResourceName = _resourceName,
                    ViolationType = ViolationType.ConcurrentWrite,
                    ThreadId = currentThreadId,
                    Timestamp = currentAccess.Timestamp,
                    Details = $"Concurrent write to {_resourceName} from threads {currentThreadId} and {threadId}"
                };
            }
        }

        return null;
    }

    public ThreadAccessSummary GetSummary()
    {
        lock (_accessLock)
        {
            var totalAccess = _threadAccess.Values.Sum(list => list.Count);
            var writeAccess = _threadAccess.Values.Sum(list => list.Count(r => r.IsWrite));
            var threadCount = _threadAccess.Count;

            return new ThreadAccessSummary
            {
                ResourceName = _resourceName,
                TotalAccess = totalAccess,
                WriteAccess = writeAccess,
                ReadAccess = totalAccess - writeAccess,
                ThreadCount = threadCount
            };
        }
    }
}

public record AccessRecord(string Operation, bool IsWrite, DateTime Timestamp);

public record ThreadSafetyStatistics
{
    public long TotalValidations { get; init; }
    public long TotalViolations { get; init; }
    public int ActiveThreads { get; init; }
    public int MonitoredResources { get; init; }
    public int RecentViolations { get; init; }
    public double ViolationRate { get; init; }
}

public record ThreadAccessSummary
{
    public string ResourceName { get; init; } = string.Empty;
    public int TotalAccess { get; init; }
    public int WriteAccess { get; init; }
    public int ReadAccess { get; init; }
    public int ThreadCount { get; init; }
}

public record ThreadSafetyViolation
{
    public string ResourceName { get; init; } = string.Empty;
    public ViolationType ViolationType { get; init; }
    public int ThreadId { get; init; }
    public DateTime Timestamp { get; init; }
    public string Details { get; init; } = string.Empty;
}

public enum ViolationType
{
    ConcurrentWrite,
    LockNotHeld,
    AtomicViolation,
    RaceCondition
}
