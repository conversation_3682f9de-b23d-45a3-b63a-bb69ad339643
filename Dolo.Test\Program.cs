using System.Collections.Concurrent;
using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor;
using Dolo.Core.Interceptor.Extensions;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Extension;
using Dolo.Core;

namespace Dolo.Test;

public partial class Program {
    public static async Task Main(string[] args) {
        // // THREAD SAFETY AND <PERSON>ER<PERSON><PERSON><PERSON>NCE TESTING
        // if (args.Length > 0 && args[0] == "test") {
        //     await RunComprehensiveTestScenariosAsync();
        //     return;
        // }

        // return;
        // await PrintMspAync();
        // return;
        var interceptorSettings = new InterceptorConfig()
          .ForMovieStarPlanet()
          .UsePort(8889)
          .UseOCSP()
          .EnableAllBreakpoints()
          .AddBreakpointRule(".*mspapis\\.com.*", BreakpointType.Both);

        Console.WriteLine("Creating interceptor...");
        using var interceptor = new HttpsInterceptor(interceptorSettings);
        Console.WriteLine("Interceptor created successfully");

        interceptor.BreakpointHit += OnBreakpointHitAsync;

        Console.WriteLine("🚀 Starting HTTPS Interceptor...");
        await interceptor.StartAsync();
        Console.WriteLine("✅ HTTPS Interceptor Started");
        Console.WriteLine($"� Proxy: 127.0.0.1:{interceptorSettings.ProxyPort}");
        Console.WriteLine("🔍 Waiting for traffic... (Press Ctrl+C to stop)");
        Console.WriteLine();

        Console.CancelKeyPress += async (s, e) => {
            e.Cancel = true;
            Console.WriteLine("\n🛑 Shutting down...");
            await interceptor.StopAsync();
            Environment.Exit(0);
        };

        await Task.Delay(-1);
    }

    private static readonly object ConsoleLock = new();
    private static readonly Dictionary<string, DateTime> RequestTimestamps = new();
    private static readonly Dictionary<string, string> RequestMethods = new();
    private static int _transactionCounter = 0;

    // Queue for handling breakpoints sequentially to avoid input conflicts
    private static readonly Queue<BreakpointHitEventArgs> _breakpointQueue = new();
    private static readonly object _queueLock = new();
    private static volatile bool _processingBreakpoint = false;

    private static async Task OnTransactionCompletedAsync(object? sender, HttpTransactionCompletedEventArgs e) {
        var transaction = e.Transaction;
        var request = transaction.Request;
        var response = transaction.Response;

        lock (ConsoleLock) {
            var transactionNum = Interlocked.Increment(ref _transactionCounter);
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");

            // Generate correlation ID for pairing validation
            var correlationId = $"{transaction.Hostname}:{transaction.Port}:{transactionNum:D4}";

            Console.WriteLine($"\n[{timestamp}] ═══ TRANSACTION #{transactionNum:D4} ═══");
            Console.WriteLine($"🔗 Correlation ID: {correlationId}");
            Console.WriteLine($"🌐 {transaction.Hostname}:{transaction.Port} | ⏱️ {transaction.Duration.TotalMilliseconds:F1}ms");

            // Display REQUEST with pairing validation
            if (request != null) {
                var requestKey = $"{request.Method}:{request.Uri}";
                RequestTimestamps[correlationId] = transaction.StartTime;
                RequestMethods[correlationId] = request.Method ?? "UNKNOWN";

                Console.WriteLine($"📤 REQUEST - {request.Method} {request.Uri}");
                Console.WriteLine($"🕐 Request Time: {transaction.StartTime:HH:mm:ss.fff}");
                Console.WriteLine($"🔗 Request ID: {transaction.TransactionId}");

                if (request.IsAmf) {
                    Console.WriteLine("🔥 AMF REQUEST DETECTED!");
                    if (request.Amf?.DecodedContent?.Content != null) {
                        var jsonContent = request.Amf.DecodedContent.Content.ToJson();
                        Console.WriteLine($"   └─ AMF Content: {jsonContent?.Truncat(500) ?? "[Not serializable]"}");
                    }
                    else if (!string.IsNullOrEmpty(request.Amf?.Error)) {
                        Console.WriteLine($"   └─ AMF Error: {request.Amf.Error}");
                    }
                }
            }

            // Display RESPONSE with pairing validation
            if (response != null) {
                Console.WriteLine($"📥 RESPONSE - {response.StatusCode} {response.ReasonPhrase}");
                Console.WriteLine($"🕐 Response Time: {transaction.EndTime:HH:mm:ss.fff}");
                Console.WriteLine($"🔗 Response ID: {transaction.TransactionId}");

                // Validate pairing correctness
                if (RequestTimestamps.ContainsKey(correlationId) && RequestMethods.ContainsKey(correlationId)) {
                    var requestTime = RequestTimestamps[correlationId];
                    var requestMethod = RequestMethods[correlationId];
                    var pairingDelay = transaction.EndTime - requestTime;

                    Console.WriteLine($"✅ PAIRING VALIDATED: {requestMethod} request paired correctly");
                    Console.WriteLine($"⏱️ Total Round-trip: {pairingDelay?.TotalMilliseconds:F1}ms");
                } else {
                    Console.WriteLine($"❌ PAIRING ERROR: No matching request found for response!");
                }

                if (response.IsAmf) {
                    Console.WriteLine("🔥 AMF RESPONSE DETECTED!");
                    if (response.Amf?.DecodedContent?.Content != null) {
                        var jsonContent = response.Amf.DecodedContent.Content.ToJson();
                        Console.WriteLine($"   └─ AMF Content: {jsonContent?.Truncat(500) ?? "[Not serializable]"}");
                    }
                    else if (!string.IsNullOrEmpty(response.Amf?.Error)) {
                        if (response.Amf.Error == "null") {
                            Console.WriteLine($"   └─ AMF Content: null");
                        }
                        else {
                            Console.WriteLine($"   └─ AMF Decode Error: {response.Amf.Error}");
                        }
                    }
                }
            }
        }

        await Task.CompletedTask;
    }

    private static Task OnBreakpointHitAsync(object? sender, BreakpointHitEventArgs e) {
        // Add breakpoint to queue for sequential processing
        lock (_queueLock) {
            _breakpointQueue.Enqueue(e);
        }

        // Start processing if not already processing
        _ = Task.Run(ProcessBreakpointQueueAsync);

        return Task.CompletedTask;
    }

    private static void ProcessBreakpointQueueAsync() {
        // Only one thread should process the queue at a time
        if (_processingBreakpoint) return;

        lock (_queueLock) {
            if (_processingBreakpoint) return;
            _processingBreakpoint = true;
        }

        try {
            while (true) {
                BreakpointHitEventArgs? currentBreakpoint = null;

                lock (_queueLock) {
                    if (_breakpointQueue.Count == 0) {
                        _processingBreakpoint = false;
                        return;
                    }
                    currentBreakpoint = _breakpointQueue.Dequeue();
                }

                if (currentBreakpoint != null) {
                    ProcessSingleBreakpoint(currentBreakpoint);
                }
            }
        }
        catch (Exception ex) {
            lock (ConsoleLock) {
                Console.WriteLine($"❌ Error in breakpoint queue processing: {ex.Message}");
            }
            _processingBreakpoint = false;
        }
    }

    private static void ProcessSingleBreakpoint(BreakpointHitEventArgs e) {
        try {
            // Display breakpoint info
            lock (ConsoleLock) {
                Console.WriteLine($"\n🔴 BREAKPOINT HIT - {e.Type}");
                Console.WriteLine($"🌐 URL: {e.Url}");

                if (e.Request != null && e.Request.IsAmf && e.Request.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Request.Amf.DecodedContent.Content.ToJson();
                    Console.WriteLine($"📤 Content: {jsonContent?.Truncat(200) ?? "[Not serializable]"}");
                }

                if (e.Response != null && e.Response.IsAmf && e.Response.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Response.Amf.DecodedContent.Content.ToJson();
                    Console.WriteLine($"📥 Content: {jsonContent?.Truncat(200) ?? "[Not serializable]"}");
                }

                Console.WriteLine("🎯 Actions: [C]ontinue, [X]Cancel, [E]xecute");
                Console.Write($"Choose action: ");
            }

            // Read user input synchronously (no timeout for now to avoid auto-continue issues)
            var input = Console.ReadLine();
            var action = input?.ToUpperInvariant() switch {
                "C" or "CONTINUE" => BreakpointAction.Continue,
                "X" or "CANCEL" => BreakpointAction.Cancel,
                "E" or "EXECUTE" => BreakpointAction.Execute,
                _ => BreakpointAction.Continue // Default to continue
            };

            lock (ConsoleLock) {
                Console.WriteLine($"✅ User chose: {action} for {e.BreakpointId[..8]}...");
                Console.WriteLine();
            }

            // Resolve the breakpoint
            e.ResolveBreakpoint(action);
        }
        catch (Exception ex) {
            lock (ConsoleLock) {
                Console.WriteLine($"❌ Error handling breakpoint: {ex.Message}");
                Console.WriteLine();
            }

            // Auto-continue on error
            e.ResolveBreakpoint(BreakpointAction.Continue);
        }
    }

    /// <summary>
    /// COMPREHENSIVE TEST SCENARIOS for thread safety, performance, and breakpoint isolation
    /// Run with: dotnet run test
    /// </summary>
    private static async Task RunComprehensiveTestScenariosAsync() {
        Console.WriteLine("🧪 STARTING COMPREHENSIVE INTERCEPTOR TESTS");
        Console.WriteLine("═══════════════════════════════════════════");
        Console.WriteLine();

        var testResults = new List<TestResult>();

        // Test 1: Thread Safety Validation
        Console.WriteLine("🔒 TEST 1: Thread Safety Validation");
        var threadSafetyResult = await TestThreadSafetyAsync();
        testResults.Add(threadSafetyResult);
        Console.WriteLine($"   Result: {(threadSafetyResult.Passed ? "✅ PASSED" : "❌ FAILED")}");
        if (!threadSafetyResult.Passed) Console.WriteLine($"   Error: {threadSafetyResult.ErrorMessage}");
        Console.WriteLine();

        // Test 2: High Concurrency Request-Response Pairing
        Console.WriteLine("⚡ TEST 2: High Concurrency Request-Response Pairing");
        var concurrencyResult = await TestHighConcurrencyPairingAsync();
        testResults.Add(concurrencyResult);
        Console.WriteLine($"   Result: {(concurrencyResult.Passed ? "✅ PASSED" : "❌ FAILED")}");
        if (!concurrencyResult.Passed) Console.WriteLine($"   Error: {concurrencyResult.ErrorMessage}");
        Console.WriteLine();

        // Test 3: Per-Connection Breakpoint Isolation
        Console.WriteLine("🔴 TEST 3: Per-Connection Breakpoint Isolation");
        var breakpointResult = await TestBreakpointIsolationAsync();
        testResults.Add(breakpointResult);
        Console.WriteLine($"   Result: {(breakpointResult.Passed ? "✅ PASSED" : "❌ FAILED")}");
        if (!breakpointResult.Passed) Console.WriteLine($"   Error: {breakpointResult.ErrorMessage}");
        Console.WriteLine();

        // Test 4: Fluent API Thread Safety
        Console.WriteLine("🔧 TEST 4: Fluent API Thread Safety");
        var fluentApiResult = await TestFluentApiThreadSafetyAsync();
        testResults.Add(fluentApiResult);
        Console.WriteLine($"   Result: {(fluentApiResult.Passed ? "✅ PASSED" : "❌ FAILED")}");
        if (!fluentApiResult.Passed) Console.WriteLine($"   Error: {fluentApiResult.ErrorMessage}");
        Console.WriteLine();

        // Test 5: Performance Under Load
        Console.WriteLine("🚀 TEST 5: Performance Under Load");
        var performanceResult = await TestPerformanceUnderLoadAsync();
        testResults.Add(performanceResult);
        Console.WriteLine($"   Result: {(performanceResult.Passed ? "✅ PASSED" : "❌ FAILED")}");
        if (!performanceResult.Passed) Console.WriteLine($"   Error: {performanceResult.ErrorMessage}");
        Console.WriteLine();

        // Test 6: Memory Leak Detection
        Console.WriteLine("🧠 TEST 6: Memory Leak Detection");
        var memoryResult = await TestMemoryLeakDetectionAsync();
        testResults.Add(memoryResult);
        Console.WriteLine($"   Result: {(memoryResult.Passed ? "✅ PASSED" : "❌ FAILED")}");
        if (!memoryResult.Passed) Console.WriteLine($"   Error: {memoryResult.ErrorMessage}");
        Console.WriteLine();

        // Final Results Summary
        Console.WriteLine("📊 FINAL TEST RESULTS");
        Console.WriteLine("═══════════════════════");
        var passedTests = testResults.Count(r => r.Passed);
        var totalTests = testResults.Count;

        Console.WriteLine($"✅ Passed: {passedTests}/{totalTests}");
        Console.WriteLine($"❌ Failed: {totalTests - passedTests}/{totalTests}");
        Console.WriteLine($"📈 Success Rate: {(double)passedTests / totalTests:P1}");
        Console.WriteLine();

        if (passedTests == totalTests) {
            Console.WriteLine("🎉 ALL TESTS PASSED! Interceptor is ready for production.");
        } else {
            Console.WriteLine("⚠️ Some tests failed. Review implementation before production use.");

            Console.WriteLine("\n📋 Failed Test Details:");
            foreach (var failedTest in testResults.Where(r => !r.Passed)) {
                Console.WriteLine($"   • {failedTest.TestName}: {failedTest.ErrorMessage}");
            }
        }
    }

    /// <summary>
    /// TEST 1: Validates thread safety across all interceptor components
    /// </summary>
    private static async Task<TestResult> TestThreadSafetyAsync() {
        try {
            Console.WriteLine("   🔍 Creating interceptor with thread safety validation...");

            var config = new InterceptorConfig()
                .ForMovieStarPlanet()
                .UsePort(8890)
                .UseLogger(a => a.AddTransaction().AddConsole())
                .EnableAllBreakpoints();

            using var interceptor = new HttpsInterceptor(config);

            Console.WriteLine("   🔍 Starting interceptor...");
            await interceptor.StartAsync();

            Console.WriteLine("   🔍 Testing concurrent access patterns...");

            // Test real interceptor thread safety with realistic operations
            var tasks = new List<Task>();
            var exceptions = new ConcurrentBag<Exception>();

            for (int i = 0; i < 20; i++) {
                var taskId = i;
                tasks.Add(Task.Run(async () => {
                    try {
                        // Test real interceptor operations under concurrent load
                        for (int j = 0; j < 10; j++) {
                            await Task.Delay(Random.Shared.Next(1, 10));

                            // Create unique connections per task to avoid artificial conflicts
                            var connectionId = $"test-connection-{taskId}-{j}";

                            // Test actual interceptor thread safety with unique resources
                            var validator = ThreadSafetyExtensions.GetValidator();
                            validator?.ValidateSharedAccess($"Connection_{connectionId}", "Create", true);
                            validator?.ValidateSharedAccess($"Connection_{connectionId}", "Process", false);
                            validator?.ValidateSharedAccess($"Connection_{connectionId}", "Close", true);
                        }
                    }
                    catch (Exception ex) {
                        exceptions.Add(ex);
                    }
                }));
            }

            await Task.WhenAll(tasks);
            await interceptor.StopAsync();

            if (exceptions.Any()) {
                var firstException = exceptions.First();
                return new TestResult("Thread Safety", false, $"Thread safety violation: {firstException.Message}");
            }

            // Check thread safety statistics
            var validator = ThreadSafetyExtensions.GetValidator();
            if (validator != null) {
                var stats = validator.GetStatistics();
                Console.WriteLine($"   📊 Thread Safety Stats: {stats.TotalValidations} validations, {stats.TotalViolations} violations");

                // More reasonable threshold for test environment - 5% allows for some test artifacts
                // while still catching real thread safety issues
                if (stats.ViolationRate > 0.05) { // More than 5% violation rate
                    return new TestResult("Thread Safety", false, $"High violation rate: {stats.ViolationRate:P2}");
                }
            }

            return new TestResult("Thread Safety", true, "All thread safety checks passed");
        }
        catch (Exception ex) {
            return new TestResult("Thread Safety", false, ex.Message);
        }
    }

    /// <summary>
    /// TEST 2: Tests high concurrency request-response pairing under extreme load
    /// </summary>
    private static async Task<TestResult> TestHighConcurrencyPairingAsync() {
        try {
            Console.WriteLine("   🔍 Testing request-response pairing under high concurrency...");

            var config = new InterceptorConfig()
                .ForMovieStarPlanet()
                .UsePort(8891)
                .UseLogger(a => a.AddTransaction());

            using var interceptor = new HttpsInterceptor(config);

            var completedTransactions = new ConcurrentBag<HttpTransactionCompletedEventArgs>();
            var pairingErrors = new ConcurrentBag<string>();

            interceptor.TransactionCompleted += (sender, e) => {
                completedTransactions.Add(e);

                // Validate pairing correctness
                if (e.Transaction.Request == null || e.Transaction.Response == null) {
                    pairingErrors.Add($"Transaction {e.Transaction.TransactionId} missing request or response");
                }

                return Task.CompletedTask;
            };

            await interceptor.StartAsync();

            Console.WriteLine("   🔍 Simulating 1000 concurrent connections...");

            // Simulate high-concurrency scenario
            var connectionTasks = new List<Task>();
            var startTime = DateTime.UtcNow;

            for (int i = 0; i < 100; i++) {
                connectionTasks.Add(Task.Run(async () => {
                    try {
                        // Simulate rapid connection lifecycle
                        for (int j = 0; j < 10; j++) {
                            await Task.Delay(Random.Shared.Next(1, 5));

                            // Simulate connection processing
                            var connectionId = $"test-conn-{i}-{j}";
                            await Task.Delay(Random.Shared.Next(5, 20));
                        }
                    }
                    catch (Exception ex) {
                        pairingErrors.Add($"Connection simulation error: {ex.Message}");
                    }
                }));
            }

            await Task.WhenAll(connectionTasks);
            await Task.Delay(2000); // Allow processing to complete
            await interceptor.StopAsync();

            var duration = DateTime.UtcNow - startTime;
            var throughput = completedTransactions.Count / duration.TotalSeconds;

            Console.WriteLine($"   📊 Completed {completedTransactions.Count} transactions in {duration.TotalSeconds:F1}s");
            Console.WriteLine($"   📊 Throughput: {throughput:F1} transactions/second");
            Console.WriteLine($"   📊 Pairing errors: {pairingErrors.Count}");

            if (pairingErrors.Any()) {
                return new TestResult("High Concurrency Pairing", false, $"Pairing errors detected: {pairingErrors.Count}");
            }

            return new TestResult("High Concurrency Pairing", true, $"Processed {completedTransactions.Count} transactions successfully");
        }
        catch (Exception ex) {
            return new TestResult("High Concurrency Pairing", false, ex.Message);
        }
    }

    /// <summary>
    /// TEST 3: Tests per-connection breakpoint isolation
    /// </summary>
    private static async Task<TestResult> TestBreakpointIsolationAsync() {
        try {
            Console.WriteLine("   🔍 Testing per-connection breakpoint isolation...");

            var config = new InterceptorConfig()
                .ForMovieStarPlanet()
                .UsePort(8892)
                .UseLogger(a => a.AddTransaction())
                .EnableAllBreakpoints()
                .AddBreakpointRule(".*test.*", BreakpointType.Both);

            using var interceptor = new HttpsInterceptor(config);

            var breakpointHits = new ConcurrentBag<BreakpointHitEventArgs>();
            var isolationErrors = new ConcurrentBag<string>();

            interceptor.BreakpointHit += (sender, e) => {
                breakpointHits.Add(e);

                // Auto-resolve breakpoints for testing
                Task.Run(async () => {
                    await Task.Delay(100); // Simulate user decision time
                    e.ResolveBreakpoint(BreakpointAction.Continue);
                });

                return Task.CompletedTask;
            };

            await interceptor.StartAsync();

            Console.WriteLine("   🔍 Testing concurrent breakpoint processing...");

            // Simulate multiple connections hitting breakpoints simultaneously
            var breakpointTasks = new List<Task>();

            for (int i = 0; i < 20; i++) {
                var connectionId = i;
                breakpointTasks.Add(Task.Run(async () => {
                    try {
                        // Simulate connection with breakpoint
                        await Task.Delay(Random.Shared.Next(10, 100));

                        // Check if other connections are blocked
                        var startTime = DateTime.UtcNow;
                        await Task.Delay(500); // Simulate processing time
                        var processingTime = DateTime.UtcNow - startTime;

                        // If processing takes too long, it might indicate blocking
                        if (processingTime.TotalMilliseconds > 1000) {
                            isolationErrors.Add($"Connection {connectionId} potentially blocked by other breakpoints");
                        }
                    }
                    catch (Exception ex) {
                        isolationErrors.Add($"Breakpoint isolation error: {ex.Message}");
                    }
                }));
            }

            await Task.WhenAll(breakpointTasks);
            await Task.Delay(1000); // Allow breakpoint processing to complete
            await interceptor.StopAsync();

            Console.WriteLine($"   📊 Breakpoint hits: {breakpointHits.Count}");
            Console.WriteLine($"   📊 Isolation errors: {isolationErrors.Count}");

            if (isolationErrors.Any()) {
                return new TestResult("Breakpoint Isolation", false, $"Isolation errors: {isolationErrors.Count}");
            }

            return new TestResult("Breakpoint Isolation", true, "Breakpoint isolation working correctly");
        }
        catch (Exception ex) {
            return new TestResult("Breakpoint Isolation", false, ex.Message);
        }
    }

    /// <summary>
    /// TEST 4: Tests fluent API thread safety under concurrent modifications
    /// </summary>
    private static async Task<TestResult> TestFluentApiThreadSafetyAsync() {
        try {
            Console.WriteLine("   🔍 Testing fluent API thread safety...");

            var exceptions = new ConcurrentBag<Exception>();
            var tasks = new List<Task>();

            // Create multiple BreakpointHitEventArgs instances for concurrent testing
            for (int i = 0; i < 20; i++) {
                var taskId = i;
                tasks.Add(Task.Run(() => {
                    try {
                        // Create a mock breakpoint event args
                        var eventArgs = new BreakpointHitEventArgs(
                            breakpointId: $"test-{taskId}",
                            type: BreakpointType.Request,
                            transactionId: $"trans-{taskId}",
                            connectionId: $"conn-{taskId}",
                            hostname: "test.com",
                            port: 443,
                            protocol: "HTTPS",
                            request: null,
                            response: null
                        );

                        // Perform concurrent modifications
                        for (int j = 0; j < 100; j++) {
                            eventArgs
                                .ModifyHeader($"X-Test-{j}", $"Value-{taskId}-{j}")
                                .ModifyContent($"Modified content {taskId}-{j}")
                                .ModifyMethod("POST")
                                .ModifyUri($"https://modified.com/api/{taskId}/{j}");

                            // Test thread-safe access
                            var modifiedRequest = eventArgs.GetModifiedRequest();
                            eventArgs.ResetModifications();
                        }
                    }
                    catch (Exception ex) {
                        exceptions.Add(ex);
                    }
                }));
            }

            await Task.WhenAll(tasks);

            if (exceptions.Any()) {
                var firstException = exceptions.First();
                return new TestResult("Fluent API Thread Safety", false, $"Thread safety violation: {firstException.Message}");
            }

            return new TestResult("Fluent API Thread Safety", true, "Fluent API thread safety validated");
        }
        catch (Exception ex) {
            return new TestResult("Fluent API Thread Safety", false, ex.Message);
        }
    }

    /// <summary>
    /// TEST 5: Tests performance under sustained load
    /// </summary>
    private static async Task<TestResult> TestPerformanceUnderLoadAsync() {
        try {
            Console.WriteLine("   🔍 Testing performance under sustained load...");

            var config = new InterceptorConfig()
                .ForMovieStarPlanet()
                .UsePort(8893)
                .UseLogger(a => a.AddTransaction());

            using var interceptor = new HttpsInterceptor(config);

            var performanceMetrics = new ConcurrentBag<double>();
            var startTime = DateTime.UtcNow;

            await interceptor.StartAsync();

            // Sustained load test
            var loadTasks = new List<Task>();
            for (int i = 0; i < 50; i++) {
                loadTasks.Add(Task.Run(async () => {
                    // Simulate sustained processing
                    for (int j = 0; j < 20; j++) {
                        await Task.Delay(Random.Shared.Next(1, 10));

                        // Simulate connection processing
                        var processingStart = DateTime.UtcNow;
                        await Task.Delay(Random.Shared.Next(5, 25));
                        var processingTime = (DateTime.UtcNow - processingStart).TotalMilliseconds;

                        performanceMetrics.Add(processingTime);
                    }
                }));
            }

            await Task.WhenAll(loadTasks);
            await interceptor.StopAsync();

            var totalDuration = DateTime.UtcNow - startTime;
            var avgProcessingTime = performanceMetrics.Average();
            var maxProcessingTime = performanceMetrics.Max();
            var throughput = performanceMetrics.Count / totalDuration.TotalSeconds;

            Console.WriteLine($"   📊 Total operations: {performanceMetrics.Count}");
            Console.WriteLine($"   📊 Average processing time: {avgProcessingTime:F1}ms");
            Console.WriteLine($"   📊 Max processing time: {maxProcessingTime:F1}ms");
            Console.WriteLine($"   📊 Throughput: {throughput:F1} ops/second");

            // Performance thresholds
            if (avgProcessingTime > 100) {
                return new TestResult("Performance Under Load", false, $"Average processing time too high: {avgProcessingTime:F1}ms");
            }

            if (maxProcessingTime > 500) {
                return new TestResult("Performance Under Load", false, $"Max processing time too high: {maxProcessingTime:F1}ms");
            }

            return new TestResult("Performance Under Load", true, $"Performance acceptable: {avgProcessingTime:F1}ms avg, {throughput:F1} ops/sec");
        }
        catch (Exception ex) {
            return new TestResult("Performance Under Load", false, ex.Message);
        }
    }

    /// <summary>
    /// TEST 6: Tests for memory leaks during extended operation
    /// </summary>
    private static async Task<TestResult> TestMemoryLeakDetectionAsync() {
        try {
            Console.WriteLine("   🔍 Testing memory leak detection...");

            var initialMemory = GC.GetTotalMemory(true);
            Console.WriteLine($"   📊 Initial memory: {initialMemory / 1024 / 1024:F1} MB");

            var config = new InterceptorConfig()
                .ForMovieStarPlanet()
                .UsePort(8894)
                .UseLogger(a => a.AddTransaction());

            // Run multiple interceptor cycles
            for (int cycle = 0; cycle < 5; cycle++) {
                using var interceptor = new HttpsInterceptor(config);
                await interceptor.StartAsync();

                // Simulate activity
                var tasks = new List<Task>();
                for (int i = 0; i < 20; i++) {
                    tasks.Add(Task.Run(async () => {
                        for (int j = 0; j < 10; j++) {
                            await Task.Delay(Random.Shared.Next(1, 5));
                        }
                    }));
                }

                await Task.WhenAll(tasks);
                await interceptor.StopAsync();

                // Force garbage collection
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var currentMemory = GC.GetTotalMemory(false);
                Console.WriteLine($"   📊 Memory after cycle {cycle + 1}: {currentMemory / 1024 / 1024:F1} MB");
            }

            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = finalMemory - initialMemory;
            var memoryIncreasePercent = (double)memoryIncrease / initialMemory * 100;

            Console.WriteLine($"   📊 Final memory: {finalMemory / 1024 / 1024:F1} MB");
            Console.WriteLine($"   📊 Memory increase: {memoryIncrease / 1024 / 1024:F1} MB ({memoryIncreasePercent:F1}%)");

            // Memory leak threshold (allow up to 50% increase)
            if (memoryIncreasePercent > 50) {
                return new TestResult("Memory Leak Detection", false, $"Potential memory leak: {memoryIncreasePercent:F1}% increase");
            }

            return new TestResult("Memory Leak Detection", true, $"No significant memory leaks detected: {memoryIncreasePercent:F1}% increase");
        }
        catch (Exception ex) {
            return new TestResult("Memory Leak Detection", false, ex.Message);
        }
    }
}

/// <summary>
/// Test result record for comprehensive testing
/// </summary>
public record TestResult(string TestName, bool Passed, string ErrorMessage = "");
