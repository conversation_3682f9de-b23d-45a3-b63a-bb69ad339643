
using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Linq;
using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor;
using Dolo.Core.Interceptor.Extensions;
using Dolo.Core.Extension;
using Dolo.Core;
namespace Dolo.Test;

public class Program {
    private static readonly object ConsoleLock = new();

    // Native Windows API for MessageBox
    [DllImport("user32.dll", CharSet = CharSet.Unicode)]
    private static extern int MessageBox(IntPtr hWnd, string text, string caption, uint type);

    // MessageBox button types
    private const uint MB_YESNOCANCEL = 0x00000003;
    private const uint MB_ICONQUESTION = 0x00000020;
    private const uint MB_DEFBUTTON1 = 0x00000000;

    // MessageBox return values
    private const int IDYES = 6;
    private const int IDNO = 7;
    private const int IDCANCEL = 2;

    // Random name generator for Profile Identity API
    private static readonly string[] RandomNames = [
        "VVVV", "XSS", "SDSSD", "WWWWRQ", "FFFSAF", "ASCAS", "AFASFA", "AASf"
    ];

    private static readonly Random Random = new();

    private static string GetRandomName() => RandomNames[Random.Next(RandomNames.Length)];





    private static string ReplaceProfileNames(string content) {
        // Handle the Profile Identity API response format: 16\n["Axa","Affogato","3"]\n0
        // Replace names in JSON array with random names

        var lines = content.Split('\n');
        if (lines.Length >= 2) {
            var jsonLine = lines[1];

            // Try to parse as JSON array
            try {
                if (JsonSerializer.Deserialize<string[]>(jsonLine) is { } names) {
                    // Replace each name with a random one
                    var randomNames = names.Select(_ => GetRandomName()).ToArray();
                    var newJsonLine = JsonSerializer.Serialize(randomNames);

                    // Reconstruct the response
                    lines[1] = newJsonLine;
                    return string.Join('\n', lines);
                }
            }
            catch {
                // If JSON parsing fails, use regex fallback
                var namePattern = @"""([^""]+)""";
                var modifiedLine = Regex.Replace(jsonLine, namePattern, _ => $"\"{GetRandomName()}\"");
                lines[1] = modifiedLine;
                return string.Join('\n', lines);
            }
        }

        return content; // Return original if parsing fails
    }

    [STAThread]
    public static async Task Main(string[] args) {
        var interceptorSettings = new InterceptorConfig()
          .ForMovieStarPlanet()
          .UsePort(8889)
          .UseOCSP()
          .EnableAllBreakpoints()
          .AddBreakpointRule(".*profileidentity.*", BreakpointType.Both) // Target Profile Identity API endpoints directly
          .UseSequentialBreakpoints(); // Sequential processing by default

        // Allow user to choose breakpoint processing mode
        Console.WriteLine("🔧 Breakpoint Processing Modes:");
        Console.WriteLine("   [S] Sequential (Default) - Reliable input handling, no conflicts");
        Console.WriteLine("   [C] Concurrent - Faster, potential input conflicts");
        Console.Write("Choose mode (S/C) or press Enter for default: ");

        var modeInput = Console.ReadLine()?.ToUpperInvariant();
        if (modeInput == "C") {
            interceptorSettings.UseConcurrentBreakpoints();
            Console.WriteLine("✅ Concurrent processing enabled");
        }
        else {
            Console.WriteLine("✅ Sequential processing enabled (default)");
        }
        Console.WriteLine();

        Console.WriteLine("Creating interceptor...");
        using var interceptor = new HttpsInterceptor(interceptorSettings);
        Console.WriteLine("Interceptor created successfully");

        interceptor.BreakpointHit += OnBreakpointHitAsync;

        Console.WriteLine("🚀 Starting HTTPS Interceptor...");
        await interceptor.StartAsync();
        Console.WriteLine("✅ HTTPS Interceptor Started");
        Console.WriteLine($"🌐 Proxy: 127.0.0.1:{interceptorSettings.ProxyPort}");
        Console.WriteLine("🔍 Waiting for traffic... (Press Ctrl+C to stop)");
        Console.WriteLine();

        Console.CancelKeyPress += async (s, e) => {
            e.Cancel = true;
            Console.WriteLine("\n🛑 Shutting down...");
            await interceptor.StopAsync();
            Environment.Exit(0);
        };

        await Task.Delay(-1);
    }

    private static Task OnBreakpointHitAsync(object? sender, BreakpointHitEventArgs e) {
        // The library handles sequential vs concurrent processing internally
        // We just need to handle the user interaction
        _ = Task.Run(() => ProcessBreakpoint(e));
        return Task.CompletedTask;
    }

    private static void ProcessBreakpoint(BreakpointHitEventArgs e) {
        try {
            var breakpointId = e.BreakpointId[..8];

            // Pre-process Profile Identity API responses for modification
            // Since breakpoint rule targets profileidentity endpoints directly,
            // we can modify all response content without additional filtering
            string? modifiedContent = null;
            if (e.Type == BreakpointType.Response && e.Response?.Content?.Length > 0) {
                var originalContent = System.Text.Encoding.UTF8.GetString(e.Response.Content);
                modifiedContent = ReplaceProfileNames(originalContent);

                // Apply the modification immediately so BreakpointManager can use it
                e.ModifyContent(modifiedContent);

                lock (ConsoleLock) {
                    Console.WriteLine($"🔧 Pre-applied Profile Identity API name modifications for {breakpointId}");
                    Console.WriteLine($"🔧 Original content: {originalContent}");
                    Console.WriteLine($"🔧 Modified content: {modifiedContent}");
                    Console.WriteLine($"🔧 HasModifications: {e.HasModifications}");
                }
            }

            // Build full URL with path for better visibility
            var fullUrl = e.Url;
            if (e.Request?.Uri != null) {
                fullUrl = e.Request.Uri.ToString();
            }
            else if (e.Response?.Uri != null) {
                fullUrl = e.Response.Uri.ToString();
            }

            // Build message content for MessageBox
            var message = $"🔴 BREAKPOINT HIT - {e.Type}\n";
            message += $"🆔 ID: {breakpointId}\n";
            message += $"🌐 URL: {fullUrl}\n\n";

            // Show Request Content (AMF or HTTP)
            if (e.Request != null) {
                if (e.Request.IsAmf && e.Request.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Request.Amf.DecodedContent.Content.ToJson();
                    message += $"📤 REQUEST AMF:\n{jsonContent ?? "[Not serializable]"}\n\n";
                }
                else if (e.Request.Content?.Length > 0) {
                    var content = System.Text.Encoding.UTF8.GetString(e.Request.Content);
                    message += $"📤 REQUEST HTTP:\n{content}\n\n";
                }
            }

            // Show Response Content (AMF or HTTP)
            if (e.Response != null) {
                if (e.Response.IsAmf && e.Response.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Response.Amf.DecodedContent.Content.ToJson();
                    message += $"📥 RESPONSE AMF:\n{jsonContent ?? "[Not serializable]"}\n\n";
                }
                else if (e.Response.Content?.Length > 0) {
                    var content = System.Text.Encoding.UTF8.GetString(e.Response.Content);

                    // Show modified content if we have it, otherwise show original
                    if (modifiedContent != null) {
                        message += $"📥 RESPONSE HTTP (MODIFIED):\n{modifiedContent}\n\n";
                        message += $"🔧 ORIGINAL:\n{content}\n\n";
                    }
                    else {
                        message += $"📥 RESPONSE HTTP:\n{content}\n\n";
                    }
                }
            }

            message += "Choose action:\n";
            message += "• YES = Continue\n";
            message += "• NO = Cancel\n";
            message += "• CANCEL = Execute";

            // Show native MessageBox
            var result = MessageBox(IntPtr.Zero, message, "🔴 MSP Profile Identity API",
                MB_YESNOCANCEL | MB_ICONQUESTION | MB_DEFBUTTON1);

            var action = result switch {
                IDYES => BreakpointAction.Continue,
                IDNO => BreakpointAction.Cancel,
                IDCANCEL => BreakpointAction.Execute,
                _ => BreakpointAction.Continue // Default to continue
            };

            // Log the action taken
            if (action == BreakpointAction.Execute && e.HasModifications) {
                lock (ConsoleLock) {
                    Console.WriteLine($"🔧 User chose Execute - modifications will be applied by BreakpointManager for {breakpointId}");
                }
            }

            // Log the action to console
            lock (ConsoleLock) {
                Console.WriteLine($"✅ User chose: {action} for {breakpointId} via MessageBox");
            }

            e.ResolveBreakpoint(action);
        }
        catch (Exception ex) {
            lock (ConsoleLock) {
                Console.WriteLine($"❌ Error handling breakpoint: {ex.Message}");
                Console.WriteLine();
            }

            // Auto-continue on error
            e.ResolveBreakpoint(BreakpointAction.Continue);
        }
    }
}
