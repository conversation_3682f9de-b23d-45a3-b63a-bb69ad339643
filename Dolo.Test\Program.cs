
using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor;
using Dolo.Core.Interceptor.Extensions;
using Dolo.Core.Extension;
using Dolo.Core;

namespace Dolo.Test;

public class Program {
    private static readonly object ConsoleLock = new();

    [STAThread]
    public static async Task Main(string[] args) {
        var interceptorSettings = new InterceptorConfig()
          .ForMovieStarPlanet()
          .UsePort(8889)
          .UseOCSP()
          .EnableAllBreakpoints()
          .AddBreakpointRule(".*msp.*\\.com.*", BreakpointType.Both) // Catch all MSP domains
          .UseSequentialBreakpoints(); // Sequential processing by default

        // Allow user to choose breakpoint processing mode
        Console.WriteLine("🔧 Breakpoint Processing Modes:");
        Console.WriteLine("   [S] Sequential (Default) - Reliable input handling, no conflicts");
        Console.WriteLine("   [C] Concurrent - Faster, potential input conflicts");
        Console.Write("Choose mode (S/C) or press Enter for default: ");

        var modeInput = Console.ReadLine()?.ToUpperInvariant();
        if (modeInput == "C") {
            interceptorSettings.UseConcurrentBreakpoints();
            Console.WriteLine("✅ Concurrent processing enabled");
        }
        else {
            Console.WriteLine("✅ Sequential processing enabled (default)");
        }
        Console.WriteLine();

        Console.WriteLine("Creating interceptor...");
        using var interceptor = new HttpsInterceptor(interceptorSettings);
        Console.WriteLine("Interceptor created successfully");

        interceptor.BreakpointHit += OnBreakpointHitAsync;

        Console.WriteLine("🚀 Starting HTTPS Interceptor...");
        await interceptor.StartAsync();
        Console.WriteLine("✅ HTTPS Interceptor Started");
        Console.WriteLine($"🌐 Proxy: 127.0.0.1:{interceptorSettings.ProxyPort}");
        Console.WriteLine("🔍 Waiting for traffic... (Press Ctrl+C to stop)");
        Console.WriteLine();

        Console.CancelKeyPress += async (s, e) => {
            e.Cancel = true;
            Console.WriteLine("\n🛑 Shutting down...");
            await interceptor.StopAsync();
            Environment.Exit(0);
        };

        await Task.Delay(-1);
    }

    private static Task OnBreakpointHitAsync(object? sender, BreakpointHitEventArgs e) {
        // The library handles sequential vs concurrent processing internally
        // We just need to handle the user interaction
        _ = Task.Run(() => ProcessBreakpoint(e));
        return Task.CompletedTask;
    }

    private static void ProcessBreakpoint(BreakpointHitEventArgs e) {
        try {
            var breakpointId = e.BreakpointId[..8];

            // Display breakpoint info
            lock (ConsoleLock) {
                Console.WriteLine($"\n🔴 BREAKPOINT HIT - {e.Type} - ID: {breakpointId}");

                // Build full URL with path for better visibility
                var fullUrl = e.Url;
                if (e.Request?.Uri != null) {
                    fullUrl = e.Request.Uri.ToString();
                }
                else if (e.Response?.Uri != null) {
                    fullUrl = e.Response.Uri.ToString();
                }

                Console.WriteLine($"🌐 URL: {fullUrl}");

                if (e.Request != null && e.Request.IsAmf && e.Request.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Request.Amf.DecodedContent.Content.ToJson();
                    Console.WriteLine($"📤 Content: {jsonContent?.Truncat(200) ?? "[Not serializable]"}");
                }

                if (e.Response != null && e.Response.IsAmf && e.Response.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Response.Amf.DecodedContent.Content.ToJson();
                    Console.WriteLine($"📥 Content: {jsonContent?.Truncat(200) ?? "[Not serializable]"}");
                }

                Console.WriteLine("🎯 Actions: [C]ontinue, [X]Cancel, [E]xecute");
                Console.Write($"Choose action for {breakpointId}: ");
            }

            // Read user input (library handles sequential vs concurrent processing)
            var input = Console.ReadLine();
            var action = input?.ToUpperInvariant() switch {
                "C" or "CONTINUE" => BreakpointAction.Continue,
                "X" or "CANCEL" => BreakpointAction.Cancel,
                "E" or "EXECUTE" => BreakpointAction.Execute,
                _ => BreakpointAction.Continue // Default to continue
            };

            lock (ConsoleLock) {
                Console.WriteLine($"✅ User chose: {action} for {breakpointId}");
                Console.WriteLine();
            }

            e.ResolveBreakpoint(action);
        }
        catch (Exception ex) {
            lock (ConsoleLock) {
                Console.WriteLine($"❌ Error handling breakpoint: {ex.Message}");
                Console.WriteLine();
            }

            // Auto-continue on error
            e.ResolveBreakpoint(BreakpointAction.Continue);
        }
    }
}
