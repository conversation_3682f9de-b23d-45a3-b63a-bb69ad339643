
using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor;
using Dolo.Core.Interceptor.Extensions;
using Dolo.Core.Extension;
using Dolo.Core;
namespace Dolo.Test;

public class Program {
    private static readonly object ConsoleLock = new();

    // Native Windows API for MessageBox
    [DllImport("user32.dll", CharSet = CharSet.Unicode)]
    private static extern int MessageBox(IntPtr hWnd, string text, string caption, uint type);

    // MessageBox button types
    private const uint MB_YESNOCANCEL = 0x00000003;
    private const uint MB_ICONQUESTION = 0x00000020;
    private const uint MB_DEFBUTTON1 = 0x00000000;

    // MessageBox return values
    private const int IDYES = 6;
    private const int IDNO = 7;
    private const int IDCANCEL = 2;

    [STAThread]
    public static async Task Main(string[] args) {
        var interceptorSettings = new InterceptorConfig()
          .ForMovieStarPlanet()
          .UsePort(8889)
          .UseOCSP()
          .EnableAllBreakpoints()
          .AddBreakpointRule(".*mspapis.*\\.com.*", BreakpointType.Both) // Catch all MSP domains
          .UseSequentialBreakpoints(); // Sequential processing by default

        // Allow user to choose breakpoint processing mode
        Console.WriteLine("🔧 Breakpoint Processing Modes:");
        Console.WriteLine("   [S] Sequential (Default) - Reliable input handling, no conflicts");
        Console.WriteLine("   [C] Concurrent - Faster, potential input conflicts");
        Console.Write("Choose mode (S/C) or press Enter for default: ");

        var modeInput = Console.ReadLine()?.ToUpperInvariant();
        if (modeInput == "C") {
            interceptorSettings.UseConcurrentBreakpoints();
            Console.WriteLine("✅ Concurrent processing enabled");
        }
        else {
            Console.WriteLine("✅ Sequential processing enabled (default)");
        }
        Console.WriteLine();

        Console.WriteLine("Creating interceptor...");
        using var interceptor = new HttpsInterceptor(interceptorSettings);
        Console.WriteLine("Interceptor created successfully");

        interceptor.BreakpointHit += OnBreakpointHitAsync;

        Console.WriteLine("🚀 Starting HTTPS Interceptor...");
        await interceptor.StartAsync();
        Console.WriteLine("✅ HTTPS Interceptor Started");
        Console.WriteLine($"🌐 Proxy: 127.0.0.1:{interceptorSettings.ProxyPort}");
        Console.WriteLine("🔍 Waiting for traffic... (Press Ctrl+C to stop)");
        Console.WriteLine();

        Console.CancelKeyPress += async (s, e) => {
            e.Cancel = true;
            Console.WriteLine("\n🛑 Shutting down...");
            await interceptor.StopAsync();
            Environment.Exit(0);
        };

        await Task.Delay(-1);
    }

    private static Task OnBreakpointHitAsync(object? sender, BreakpointHitEventArgs e) {
        // The library handles sequential vs concurrent processing internally
        // We just need to handle the user interaction
        _ = Task.Run(() => ProcessBreakpoint(e));
        return Task.CompletedTask;
    }

    private static void ProcessBreakpoint(BreakpointHitEventArgs e) {
        try {
            var breakpointId = e.BreakpointId[..8];

            // Build full URL with path for better visibility
            var fullUrl = e.Url;
            if (e.Request?.Uri != null) {
                fullUrl = e.Request.Uri.ToString();
            }
            else if (e.Response?.Uri != null) {
                fullUrl = e.Response.Uri.ToString();
            }

            // Build message content for MessageBox
            var message = $"🔴 BREAKPOINT HIT - {e.Type}\n";
            message += $"🆔 ID: {breakpointId}\n";
            message += $"🌐 URL: {fullUrl}\n\n";

            if (e.Request != null && e.Request.IsAmf && e.Request.Amf?.DecodedContent?.Content != null) {
                var jsonContent = e.Request.Amf.DecodedContent.Content.ToJson();
                message += $"📤 Request: {jsonContent?.Truncat(100) ?? "[Not serializable]"}\n\n";
            }

            if (e.Response != null && e.Response.IsAmf && e.Response.Amf?.DecodedContent?.Content != null) {
                var jsonContent = e.Response.Amf.DecodedContent.Content.ToJson();
                message += $"📥 Response: {jsonContent?.Truncat(100) ?? "[Not serializable]"}\n\n";
            }

            message += "Choose action:\n";
            message += "• YES = Continue\n";
            message += "• NO = Cancel\n";
            message += "• CANCEL = Execute";

            // Show native MessageBox
            var result = MessageBox(IntPtr.Zero, message, "🔴 Breakpoint Hit",
                MB_YESNOCANCEL | MB_ICONQUESTION | MB_DEFBUTTON1);

            var action = result switch {
                IDYES => BreakpointAction.Continue,
                IDNO => BreakpointAction.Cancel,
                IDCANCEL => BreakpointAction.Execute,
                _ => BreakpointAction.Continue // Default to continue
            };

            // Log the action to console
            lock (ConsoleLock) {
                Console.WriteLine($"✅ User chose: {action} for {breakpointId} via MessageBox");
            }

            e.ResolveBreakpoint(action);
        }
        catch (Exception ex) {
            lock (ConsoleLock) {
                Console.WriteLine($"❌ Error handling breakpoint: {ex.Message}");
                Console.WriteLine();
            }

            // Auto-continue on error
            e.ResolveBreakpoint(BreakpointAction.Continue);
        }
    }
}
