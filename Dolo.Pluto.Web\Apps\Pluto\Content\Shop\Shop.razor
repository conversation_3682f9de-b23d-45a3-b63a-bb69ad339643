@using Dolo.Pluto.Web.Apps.Pluto
@inherits ComponentBase

<div class="fixed inset-0 z-[99]">
        <!-- Full page background gradient -->
        <div class="absolute inset-0 bg-gradient-to-r from-[#2658C7] to-[#2456C3] z-0"></div>
        <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.1),transparent_70%)] z-0"></div>
        <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom,rgba(0,0,0,0.2),transparent_70%)] z-0"></div>

        <!-- Main content container -->
        <div class="relative h-full flex flex-col">
            <!-- Hero header section that replaces the titlebar -->
            <header class="relative z-10 pt-6 pb-6">
                <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                    <!-- Top navigation -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-6">
                            <button @onclick="NavigateToHome" class="group flex items-center gap-2 text-white hover:text-white transition-all duration-200">
                                <div class="p-1.5 rounded-lg bg-white/10 group-hover:bg-white/20 transition-colors duration-200">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">Back to Pluto</span>
                            </button>
                        </div>

                        <div class="flex items-center gap-4">
                            <a href="https://discord.gg/mspshop" target="_blank" class="group flex items-center gap-2 px-3 py-1.5 rounded-lg
                                   bg-white/10 hover:bg-white/20 border border-white/10
                                   transition-all duration-200 transform hover:scale-105">
                                <svg class="w-4 h-4 text-white" viewBox="0 0 71 55" fill="currentColor">
                                    <path d="M60.1045 4.8978C55.5792 2.8214 50.7265 1.2916 45.6527 0.41542C45.5603 0.39851 45.468 0.440769 45.4204 0.525289C44.7963 1.6353 44.105 3.0834 43.6209 4.2216C38.1637 3.4046 32.7345 3.4046 27.3892 4.2216C26.905 3.0581 26.1886 1.6353 25.5617 0.525289C25.5141 0.443589 25.4218 0.40133 25.3294 0.41542C20.2584 1.2888 15.4057 2.8186 10.8776 4.8978C10.8384 4.9147 10.8048 4.9429 10.7825 4.9795C1.57795 18.7309 -0.943561 32.1443 0.293408 45.3914C0.299005 45.4562 0.335386 45.5182 0.385761 45.5576C6.45866 50.0174 12.3413 52.7249 18.1147 54.5195C18.2071 54.5477 18.305 54.5139 18.3638 54.4378C19.7295 52.5728 20.9469 50.6063 21.9907 48.5383C22.0523 48.4172 21.9935 48.2735 21.8676 48.2256C19.9366 47.4931 18.0979 46.6 16.3292 45.5858C16.1893 45.5041 16.1781 45.304 16.3068 45.2082C16.679 44.9293 17.0513 44.6391 17.4067 44.3461C17.471 44.2926 17.5606 44.2813 17.6362 44.3151C29.2558 49.6202 41.8354 49.6202 53.3179 44.3151C53.3935 44.2785 53.4831 44.2898 53.5502 44.3433C53.9057 44.6363 54.2779 44.9293 54.6529 45.2082C54.7816 45.304 54.7732 45.5041 54.6333 45.5858C52.8646 46.6197 51.0259 47.4931 49.0921 48.2228C48.9662 48.6707 48.9102 48.4172 48.9718 48.5383C50.038 50.6034 51.2554 52.5699 52.5959 54.435C52.6519 54.5139 52.7526 54.5477 52.845 54.5195C58.6464 52.7249 64.529 50.0174 70.6019 45.5576C70.6551 45.5182 70.6887 45.459 70.6943 45.3942C72.1747 30.0791 68.2147 16.7757 60.1968 4.9823C60.1772 4.9429 60.1437 4.9147 60.1045 4.8978Z" />
                                </svg>
                                <span class="text-sm font-medium text-white">Join Discord</span>
                            </a>

                            @if (OAuthService.OAuthResult != null)
                            {
                                <div class="relative group">
                                    <button class="flex items-center gap-2 px-3 py-1.5 rounded-lg
                                               bg-white/10 hover:bg-white/20 border border-white/10
                                               transition-all duration-200 transform hover:scale-105">
                                        <img src="@(OAuthService.OAuthResult?.User?.AvatarUrl)" alt="User Avatar"
                                             class="w-6 h-6 rounded-lg ring-2 ring-white/20 object-cover">
                                        <div class="flex flex-col items-start -space-y-0.5">
                                            <span class="text-sm font-medium text-white">@(OAuthService.OAuthResult?.User?.Username?.Length > 8 ? OAuthService.OAuthResult.User.Username[..8] + "..." : OAuthService.OAuthResult?.User?.Username)</span>
                                            <span class="text-[11px] text-white/70">Member</span>
                                        </div>
                                        <svg class="w-4 h-4 ml-1 text-white/70 transition-transform duration-200 group-hover:rotate-180"
                                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M19 9l-7 7-7-7"/>
                                        </svg>
                                    </button>

                                    <div class="absolute right-0 mt-2 w-48 opacity-0 translate-y-2 invisible group-hover:opacity-100 group-hover:translate-y-0 group-hover:visible transition-all duration-200">
                                        <div class="rounded-xl bg-white py-2 shadow-xl ring-1 ring-black/5">
                                            <div class="px-3 py-2 border-b border-slate-100">
                                                <p class="text-xs font-medium text-slate-500">Logged in as</p>
                                                <p class="text-sm font-medium text-slate-700 truncate">@(OAuthService.OAuthResult?.User?.Username?.Length > 15 ? OAuthService.OAuthResult.User.Username[..15] + "..." : OAuthService.OAuthResult?.User?.Username)</p>
                                            </div>
                                            <button @onclick="PlutoService.SignOut"
                                                    class="flex w-full items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-slate-50">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                                </svg>
                                                Sign out
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <button @onclick="AuthenticateAsync"
                                        class="group flex items-center gap-2 px-3 py-1.5 rounded-lg
                                               bg-white/10 hover:bg-white/20 border border-white/10
                                               transition-all duration-200 transform hover:scale-105">
                                    <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                                    </svg>
                                    <span class="text-sm font-medium text-white">Authenticate</span>
                                </button>
                            }
                        </div>
                    </div>

                    <!-- Logo and Title Section (MovieStarPlanet logo removed) -->
                    <div class="flex flex-col items-center mb-6">
                        <!-- Title with glowing effect -->
                        <div class="relative isolate">
                            <div class="absolute -inset-x-20 -inset-y-10 opacity-20">
                                <div class="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-indigo-500/30 blur-3xl"></div>
                            </div>
                            <h1 class="relative text-4xl font-black font-['Inter'] text-white leading-tight text-center">
                                Pluto <span class="text-blue-200">Shop</span>
                            </h1>
                        </div>

                        <p class="mt-2 text-lg text-blue-100/90 font-['Signika'] max-w-2xl text-center">
                            Premium tools to enhance your experience
                        </p>

                        <!-- Payment icons -->
                        <div class="mt-3 flex items-center gap-4">
                            <div class="px-3 py-1.5 bg-white/10 backdrop-blur-sm rounded-full border border-white/10 flex items-center gap-2">
                                <div class="w-5 h-5 rounded-full bg-gradient-to-br from-amber-400 to-amber-500 flex items-center justify-center">
                                    <svg class="w-2.5 h-2.5 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M16.18 10.5C16.35 9.15 15.69 8.4 14.73 7.86L15.24 5.7L13.8 5.28L13.32 7.38C12.93 7.26 12.54 7.15 12.15 7.05L12.63 4.92L11.22 4.5L10.7 6.65C10.39 6.56 10.08 6.48 9.78 6.39V6.38L7.92 5.82L7.47 7.56C7.47 7.56 8.55 7.8 8.54 7.81C9.15 7.98 9.27 8.39 9.25 8.71L8.66 11.14C8.71 11.16 8.76 11.17 8.82 11.21C8.77 11.19 8.72 11.18 8.66 11.16L7.84 14.73C7.76 14.91 7.53 15.18 7.09 15.05C7.1 15.07 6.04 14.8 6.04 14.8L5 16.7L6.76 17.23C7.13 17.35 7.48 17.46 7.83 17.58L7.3 19.78L8.71 20.2L9.23 18.04C9.64 18.17 10.03 18.29 10.41 18.41L9.9 20.55L11.34 20.97L11.87 18.78C13.95 19.21 15.56 19.03 16.21 17.14C16.73 15.62 16.12 14.77 15 14.21C15.86 14.01 16.5 13.47 16.18 10.5ZM13.83 16.03C13.46 17.56 11.15 16.78 10.34 16.56L11.04 13.67C11.84 13.88 14.22 14.42 13.83 16.03ZM14.2 12.54C13.86 13.93 11.97 13.26 11.3 13.07L11.94 10.46C12.61 10.65 14.57 11.07 14.2 12.54Z" fill="currentColor" />
                                    </svg>
                                </div>
                                <span class="text-xs font-medium text-white">Crypto</span>
                            </div>

                            <div class="px-3 py-1.5 bg-white/10 backdrop-blur-sm rounded-full border border-white/10 flex items-center gap-2">
                                <div class="w-5 h-5 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                                    <svg class="w-2.5 h-2.5 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M21.5 4.5C21.5 8 19 10 16 10H13C12.7 10 12.5 10.2 12.5 10.5L11.5 16.5C11.5 16.8 11.2 17 11 17H8C7.8 17 7.6 16.8 7.7 16.5L10.9 0.5C11 0.2 11.2 0 11.5 0H19C20.5 0 21.5 1.5 21.5 4.5Z" fill="currentColor" />
                                    </svg>
                                </div>
                                <span class="text-xs font-medium text-white">PayPal</span>
                            </div>
                        </div>

                        <!-- Scroll indicator -->
                        <div class="mt-4 text-white/70 flex flex-col items-center scroll-indicator">
                            <span class="text-xs">All our available tools</span>
                            <svg class="w-5 h-5 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main shop content with scrollable section -->
            <main class="flex-1 max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 pb-4 relative z-10 flex flex-col">
                <!-- Tools List - Scrollable area -->
                <div class="flex-1 overflow-y-auto custom-scrollbar pr-2" style="max-height: calc(100vh - 270px);">
                    <div class="flex flex-col items-center justify-start gap-4 py-4">
                        <!-- Card 1: Pluto+ PREMIUM DESIGN - Refined Design -->
                        <a href="https://discord.gg/mspshop" target="_blank" class="relative w-full max-w-xl group transform transition-all duration-300 hover:-translate-y-1">
                            <!-- Card Background with Refined Gradient Effect -->
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/90 to-blue-600/90 rounded-xl opacity-90 shadow-lg group-hover:shadow-xl transition-all duration-300"></div>

                            <!-- Main Card Content -->
                            <div class="relative p-4 bg-white/95 backdrop-blur-sm rounded-xl overflow-hidden">
                                <!-- Updated Card Layout with Refined Content Area -->
                                <div class="flex items-center gap-4">
                                    <!-- Left: Product Image - Enhanced Design -->
                                    <div class="flex-shrink-0">
                                        <div class="w-28 h-28 overflow-hidden rounded-xl bg-gradient-to-b from-blue-50 to-white border border-blue-100/50 p-2 shadow-md group-hover:shadow-lg transition-all duration-200 relative">
                                            <!-- Decorative corner accents -->
                                            <div class="absolute top-0 left-0 w-2.5 h-2.5 border-t-2 border-l-2 border-blue-300/50 rounded-tl-md"></div>
                                            <div class="absolute top-0 right-0 w-2.5 h-2.5 border-t-2 border-r-2 border-blue-300/50 rounded-tr-md"></div>
                                            <div class="absolute bottom-0 left-0 w-2.5 h-2.5 border-b-2 border-l-2 border-blue-300/50 rounded-bl-md"></div>
                                            <div class="absolute bottom-0 right-0 w-2.5 h-2.5 border-b-2 border-r-2 border-blue-300/50 rounded-br-md"></div>

                                            <!-- Subtle glow effect -->
                                            <div class="absolute inset-0 bg-blue-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                                            <div class="absolute -inset-0.5 bg-blue-200/20 blur-sm rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                                            <!-- Image with subtle animation on hover -->
                                            <div class="w-full h-full flex items-center justify-center relative z-10 group-hover:scale-105 transition-transform duration-300">
                                                <img src="https://cdn.discordapp.com/attachments/1107592426366963735/1208539896608858133/file-rHKRPKQQNWeC6knTgUoZr34d.png?ex=6804cbbc&is=68037a3c&hm=ae87bd08e8dd0853bcbd7f1fa8d193f2750b97d6626a6f31d94f7273a7b13d58&" alt="Pluto+" class="w-5/6 h-5/6 object-contain drop-shadow-md group-hover:drop-shadow-lg transition-all duration-300">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right: Content Section -->
                                    <div class="flex-1 flex flex-col min-w-0">
                                        <!-- Product Header - Refined -->
                                        <div class="flex items-start justify-between mb-2">
                                            <div>
                                                <h3 class="text-lg font-bold text-gray-800 mb-0.5">Pluto<span class="text-blue-600">+</span></h3>
                                                <p class="text-xs text-gray-500">Supercharge your experience on pluto</p>
                                            </div>
                                            <div class="ml-3 bg-blue-50 px-2.5 py-1 rounded-lg shadow-sm flex items-center">
                                                <span class="text-sm font-bold text-blue-600">€10</span>
                                                <span class="ml-1 text-xs text-gray-500">one-time</span>
                                            </div>
                                        </div>

                                        <!-- Features List - Refined -->
                                        <div class="space-y-1 mb-1.5">
                                            <div class="flex items-center gap-1.5 text-xs text-gray-600">
                                                <span class="flex-shrink-0 inline-block p-0.5 bg-blue-100 rounded-full shadow-sm">
                                                    <svg class="w-2 h-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                                <span>Access to farming system</span>
                                            </div>
                                            <div class="flex items-center gap-1.5 text-xs text-gray-600">
                                                <span class="flex-shrink-0 inline-block p-0.5 bg-blue-100 rounded-full shadow-sm">
                                                    <svg class="w-2 h-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                                <span>Access to autograph feature</span>
                                            </div>
                                            <div class="flex items-center gap-1.5 text-xs text-gray-600">
                                                <span class="flex-shrink-0 inline-block p-0.5 bg-blue-100 rounded-full shadow-sm">
                                                    <svg class="w-2 h-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                                <span>Access to any new upcoming features</span>
                                            </div>
                                        </div>

                                        <!-- Action Indicator - New Elegant Design -->
                                        <div class="flex items-center justify-between mt-0.5">
                                            <div class="text-xs text-gray-500 italic">
                                                One-time payment, no subscription
                                            </div>
                                            <div class="group-hover:translate-x-0 translate-x-2 opacity-0 group-hover:opacity-100 transition-all duration-200 text-blue-600 text-xs font-semibold flex items-center">
                                                View details
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>

                        <!-- Card 2: Autograph - Refined Design -->
                        <a href="https://discord.gg/mspshop" target="_blank" class="relative w-full max-w-xl group transform transition-all duration-300 hover:-translate-y-1">
                            <!-- Card Background with Refined Gradient Effect - Consistent blue color -->
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/90 to-blue-600/90 rounded-xl opacity-90 shadow-lg group-hover:shadow-xl transition-all duration-300"></div>

                            <!-- Main Card Content -->
                            <div class="relative p-4 bg-white/95 backdrop-blur-sm rounded-xl overflow-hidden">
                                <!-- Updated Card Layout with Refined Content Area -->
                                <div class="flex items-center gap-4">
                                    <!-- Left: Product Image - Enhanced Design -->
                                    <div class="flex-shrink-0">
                                        <div class="w-28 h-28 overflow-hidden rounded-xl bg-gradient-to-b from-blue-50 to-white border border-blue-100/50 p-2 shadow-md group-hover:shadow-lg transition-all duration-200 relative">
                                            <!-- Decorative corner accents -->
                                            <div class="absolute top-0 left-0 w-2.5 h-2.5 border-t-2 border-l-2 border-blue-300/50 rounded-tl-md"></div>
                                            <div class="absolute top-0 right-0 w-2.5 h-2.5 border-t-2 border-r-2 border-blue-300/50 rounded-tr-md"></div>
                                            <div class="absolute bottom-0 left-0 w-2.5 h-2.5 border-b-2 border-l-2 border-blue-300/50 rounded-bl-md"></div>
                                            <div class="absolute bottom-0 right-0 w-2.5 h-2.5 border-b-2 border-r-2 border-blue-300/50 rounded-br-md"></div>

                                            <!-- Subtle glow effect -->
                                            <div class="absolute inset-0 bg-blue-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                                            <div class="absolute -inset-0.5 bg-blue-200/20 blur-sm rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                                            <!-- Image with subtle animation on hover -->
                                            <div class="w-full h-full flex items-center justify-center relative z-10 group-hover:scale-105 transition-transform duration-300">
                                                <img src="https://cdn.discordapp.com/attachments/944722654907219988/1188259171557179484/Group_45.png?ex=6804d7d7&is=68038657&hm=b76a534b64056627cfc498ebf4cceda8bf5ae51d64e5794e6ca8baecb3c630c8&" alt="Autograph" class="w-5/6 h-5/6 object-contain drop-shadow-md group-hover:drop-shadow-lg transition-all duration-300">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right: Content Section -->
                                    <div class="flex-1 flex flex-col min-w-0">
                                        <!-- Product Header - Refined (blue instead of purple) -->
                                        <div class="flex items-start justify-between mb-2">
                                            <div>
                                                <h3 class="text-lg font-bold text-gray-800 mb-0.5">Auto<span class="text-blue-600">graph</span></h3>
                                                <p class="text-xs text-gray-500">Automated autograph system without bots</p>
                                            </div>
                                            <div class="ml-3 bg-blue-50 px-2.5 py-1 rounded-lg shadow-sm flex items-center">
                                                <span class="text-sm font-bold text-blue-600">€20</span>
                                                <span class="ml-1 text-xs text-gray-500">one-time</span>
                                            </div>
                                        </div>

                                        <!-- Features List - Refined (blue instead of purple) -->
                                        <div class="space-y-1 mb-1.5">
                                            <div class="flex items-center gap-1.5 text-xs text-gray-600">
                                                <span class="flex-shrink-0 inline-block p-0.5 bg-blue-100 rounded-full shadow-sm">
                                                    <svg class="w-2 h-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                                <span>Easily switch between accounts</span>
                                            </div>
                                            <div class="flex items-center gap-1.5 text-xs text-gray-600">
                                                <span class="flex-shrink-0 inline-block p-0.5 bg-blue-100 rounded-full shadow-sm">
                                                    <svg class="w-2 h-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                                <span>Get autographs on all your accounts</span>
                                            </div>
                                            <div class="flex items-center gap-1.5 text-xs text-gray-600">
                                                <span class="flex-shrink-0 inline-block p-0.5 bg-blue-100 rounded-full shadow-sm">
                                                    <svg class="w-2 h-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                                <span>No limit - add as many accounts as you like</span>
                                            </div>
                                        </div>

                                        <!-- Action Indicator - Consistent blue color -->
                                        <div class="flex items-center justify-between mt-0.5">
                                            <div class="text-xs text-gray-500 italic">
                                                One-time payment, no subscription
                                            </div>
                                            <div class="group-hover:translate-x-0 translate-x-2 opacity-0 group-hover:opacity-100 transition-all duration-200 text-blue-600 text-xs font-semibold flex items-center">
                                                View details
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>

                    </div>
                </div>
            </main>
        </div>
    </div>