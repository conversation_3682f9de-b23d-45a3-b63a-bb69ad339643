﻿using Dolo.Core;
using Dolo.Core.Discord;
using Dolo.Planet;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("availability")]
    [Description("get a list of all server that this account is available on")]
    public async Task AvailabilityAsync(SlashCommandContext ctx,
        [Description("player username")] string username)
    {
        await ctx.LogAsync($"/msp availability {username}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync()) return;

        // check if shard is null
        if (Hub.MspShard is null)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » **Please use `/msp init` to initialize.**");
            return;
        }

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » **Checking availability...**");

        // init a string builder
        var builder = new StringBuffer();

        // init servers count
        var servers = 0;

        // create a list of tasks
        var tasks = Hub.MspShard.GetAll()
        .Select(async shard =>
            {

                if (!shard.User.LoggedIn)
                {
                    await builder.AppendLineAsync($"{MspClientUtil.GetServerDiscordFlag(shard.User.Server)} » unknown » **{username}**");
                    return;
                }

                var user = await shard.GetActorAsync(username);

                var serverFlag = MspClientUtil.GetServerDiscordFlag(shard.User.Server);
                var emoji = user.IsAvailable ? HubEmoji.Yes?.ToString() : HubEmoji.No?.ToString();
                var level = user.IsAvailable ? $"`level: {user.Level}`" : string.Empty;

                await builder.AppendLineAsync(user.IsDeleted
                                                  ? $"{serverFlag} » {emoji} » **{username}** `has been deleted` {level}"
                                                  : $"{serverFlag} » {emoji} » **{username}** {level}");

                if (user.IsAvailable) servers++;
            });

        await Task.WhenAll(tasks);
        await ctx.TryEditResponseAsync(await HubEmbed.AvailabilityAsync(ctx.Guild!, builder.ToBuilder(), servers));
    }
}