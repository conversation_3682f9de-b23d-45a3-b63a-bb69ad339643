# Charles UI Tool - InitializationScreen Integration Example

This is an example of how to integrate the shard InitializationScreen into the Charles UI tool.

## Required Changes

### 1. MainService Implementation
```csharp
using System;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Tool.Charles.Components.Content;

namespace Dolo.Pluto.Tool.Charles.Services;

public class MainService : IService, IMainService
{
    public Shard.Toolbox.Tool? Tool { get; set; }
    public Main Main { get; set; } = default!;

    public void StateHasChangedAsync()
    {
        Main?.TriggerStateChanged();
    }
}
```

### 2. Main Component (Main.razor)
```razor
@using System
@inherits ComponentBase
@page "/"
@using Dolo.Pluto.Tool.Charles.Services
@using Dolo.Pluto.Shard.Components

<InitializationScreen @ref="InitializationScreen" ToolName="Charles" />

<div class="h-full">
    <!-- Your Charles UI content here -->
</div>
```

### 3. Main Component Code-Behind (Main.razor.cs)
```csharp
using Microsoft.AspNetCore.Components;
using Dolo.Pluto.Tool.Charles.Services;
using Dolo.Pluto.Shard.Components;

namespace Dolo.Pluto.Tool.Charles.Components.Content;

public partial class Main : ComponentBase
{
    [Inject] private MainService MainServiceInstance { get; set; } = default!;

    internal InitializationScreen? InitializationScreen;

    protected override void OnInitialized()
    {
        MainServiceInstance.Main = this;
    }

    public void TriggerStateChanged()
    {
        StateHasChanged();
    }
}
```

### 4. MauiProgram.cs Registration
```csharp
// Register MainService as IMainService interface
builder.Services.AddScoped<IMainService>(provider => provider.GetRequiredService<MainService>());
```

This will show "Pluto Charles" in the initialization screen and look for the "charles" tool in the toolbox API.
