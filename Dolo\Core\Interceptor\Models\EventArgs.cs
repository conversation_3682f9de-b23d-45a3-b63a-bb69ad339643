using Dolo.Core.Interceptor.Interfaces;

namespace Dolo.Core.Interceptor.Models;

/// <summary>
/// Event arguments for intercepted HTTP/HTTPS traffic with proper request/response message objects
/// </summary>
public sealed class TrafficInterceptedEventArgs(
    string hostname,
    int port,
    string protocol,
    int bytesTransferred = 0,
    TimeSpan duration = default,
    IHttpInterceptedRequestMessage? request = null,
    IHttpInterceptedResponseMessage? response = null) : EventArgs
{
    public string Hostname { get; } = hostname;
    public int Port { get; } = port;
    public string Protocol { get; } = protocol;
    public string Url => $"{Protocol}://{Hostname}:{Port}{Request?.Uri?.PathAndQuery}";
    public int BytesTransferred { get; } = bytesTransferred;
    public TimeSpan Duration { get; } = duration;
    public DateTime Timestamp { get; } = DateTime.UtcNow;
    public IHttpInterceptedRequestMessage? Request { get; } = request;
    public IHttpInterceptedResponseMessage? Response { get; } = response;
}

/// <summary>
/// Event arguments for certificate generation events
/// </summary>
public class CertificateGeneratedEventArgs(
    string hostname,
    string certificateSubject,
    string thumbprint,
    DateTime validFrom,
    DateTime validTo) : EventArgs
{
    public string Hostname { get; } = hostname;
    public string CertificateSubject { get; } = certificateSubject;
    public string Thumbprint { get; } = thumbprint;
    public DateTime ValidFrom { get; } = validFrom;
    public DateTime ValidTo { get; } = validTo;
}

/// <summary>
/// Event arguments for error events
/// </summary>
public class InterceptorErrorEventArgs(string source, string message, Exception? exception = null) : EventArgs
{
    public string Source { get; } = source;
    public string Message { get; } = message;
    public Exception? Exception { get; } = exception;
    public DateTime Timestamp { get; } = DateTime.Now;
}

/// <summary>
/// Event arguments for timed out HTTP transactions
/// </summary>
public sealed class HttpTransactionTimedOutEventArgs(HttpTransaction transaction) : EventArgs
{
    public HttpTransaction Transaction { get; } = transaction;

    // Convenience properties
    public string Hostname => Transaction.Hostname;
    public int Port => Transaction.Port;
    public string Protocol => Transaction.Protocol;
    public string Url => $"{Protocol}://{Hostname}:{Port}{Transaction.Request?.Uri?.PathAndQuery}";
    public TimeSpan TimeoutDuration => Transaction.Duration;
    public DateTime Timestamp => Transaction.StartTime;
    public IHttpInterceptedRequestMessage? Request => Transaction.Request;
}

/// <summary>
/// Enum for breakpoint actions
/// </summary>
public enum BreakpointAction {
    Continue,
    Cancel,
    Execute
}

/// <summary>
/// Enum for breakpoint types
/// </summary>
[Flags]
public enum BreakpointType {
    None = 0,
    Request = 1,
    Response = 2,
    Both = Request | Response
}

/// <summary>
/// Event arguments for breakpoint hit events
/// </summary>
public sealed class BreakpointHitEventArgs : EventArgs {
    public string BreakpointId { get; }
    public BreakpointType Type { get; }
    public string TransactionId { get; }
    public string ConnectionId { get; }
    public string Hostname { get; }
    public int Port { get; }
    public string Protocol { get; }
    public string Url { get; }
    public DateTime Timestamp { get; }
    public IHttpInterceptedRequestMessage? Request { get; set; }
    public IHttpInterceptedResponseMessage? Response { get; set; }
    public TaskCompletionSource<BreakpointAction> ActionSource { get; }

    public BreakpointHitEventArgs(
        string breakpointId,
        BreakpointType type,
        string transactionId,
        string connectionId,
        string hostname,
        int port,
        string protocol,
        IHttpInterceptedRequestMessage? request = null,
        IHttpInterceptedResponseMessage? response = null) {
        BreakpointId = breakpointId;
        Type = type;
        TransactionId = transactionId;
        ConnectionId = connectionId;
        Hostname = hostname;
        Port = port;
        Protocol = protocol;
        Url = $"{Protocol}://{Hostname}:{Port}{Request?.Uri?.PathAndQuery ?? Response?.Uri?.PathAndQuery}";
        Timestamp = DateTime.UtcNow;
        Request = request;
        Response = response;
        ActionSource = new TaskCompletionSource<BreakpointAction>();
    }

    /// <summary>
    /// Resolves the breakpoint with the specified action
    /// </summary>
    public void ResolveBreakpoint(BreakpointAction action) {
        ActionSource.TrySetResult(action);
    }

    /// <summary>
    /// Waits for the breakpoint to be resolved
    /// </summary>
    public Task<BreakpointAction> WaitForResolutionAsync() {
        return ActionSource.Task;
    }
}
