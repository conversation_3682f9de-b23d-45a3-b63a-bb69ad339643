using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Models;

/// <summary>
/// Custom logging builder for the HTTPS interceptor with specific logging categories
/// </summary>
public class InterceptorLoggingBuilder
{
    private readonly ILoggingBuilder _builder;
    private bool _consoleAdded = false;    internal InterceptorLoggingBuilder(ILoggingBuilder builder)
    {
        _builder = builder;
        _builder.AddConsole(); // Default console logging
        _consoleAdded = true;

        // Block all interceptor categories by default - they must be explicitly enabled
        _builder.AddFilter("Dolo.Core.Interceptor.Services.TunnelingService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpRequestHandler", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.SslConnectionHandler", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Tunneling", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Certificate", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.CertificateService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Failure", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.ResponseDecodingService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Ssl", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Transaction", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpTransactionManager", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Main", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.HttpsInterceptor", LogLevel.None);
    }

    /// <summary>
    /// Adds console logging (enabled by default)
    /// </summary>
    public InterceptorLoggingBuilder AddConsole()
    {
        if (!_consoleAdded)
        {
            _builder.AddConsole();
            _consoleAdded = true;
        }
        return this;
    }    /// <summary>
    /// Enables logging for tunneling requests and data relay operations
    /// </summary>
    public InterceptorLoggingBuilder AddTunneling()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.TunnelingService", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpRequestHandler", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Tunneling", LogLevel.Information);
        return this;
    }    /// <summary>
    /// Enables logging for certificate generation and validation operations
    /// </summary>
    public InterceptorLoggingBuilder AddCertificate()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Certificate", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.CertificateService", LogLevel.Information);
        return this;
    }/// <summary>
    /// Enables logging for all failures and exceptions
    /// </summary>
    public InterceptorLoggingBuilder AddFailure()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Failure", LogLevel.Warning);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.ResponseDecodingService", LogLevel.Warning);
        return this;
    }    /// <summary>
    /// Enables logging for SSL handshake operations
    /// </summary>
    public InterceptorLoggingBuilder AddSsl()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.SslConnectionHandler", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Ssl", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Enables logging for HTTP transaction processing
    /// </summary>
    public InterceptorLoggingBuilder AddTransaction()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Transaction", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpTransactionManager", LogLevel.Information);
        return this;
    }/// <summary>
    /// Enables all logging categories with detailed information
    /// </summary>
    public InterceptorLoggingBuilder AddAll()
    {
        return AddTunneling()
               .AddCertificate()
               .AddFailure()
               .AddSsl()
               .AddTransaction()
               .AddMain();
    }    /// <summary>
    /// Enables logging for main interceptor operations (connection handling, startup/shutdown)
    /// </summary>
    public InterceptorLoggingBuilder AddMain()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Main", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.HttpsInterceptor", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Sets the minimum log level for all categories
    /// </summary>
    public InterceptorLoggingBuilder SetMinimumLevel(LogLevel level)
    {
        _builder.SetMinimumLevel(level);
        return this;
    }

    internal ILoggingBuilder GetBuilder() => _builder;
}
