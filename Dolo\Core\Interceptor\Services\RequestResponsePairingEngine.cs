using System.Collections.Concurrent;
using System.Diagnostics;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// HIGH-PERFORMANCE FIFO-based request-response pairing engine that ensures 100% correct correlation
/// under high concurrency by maintaining strict per-connection ordering with optimized data structures.
/// </summary>
public sealed class RequestResponsePairingEngine : IDisposable
{
    private readonly ConcurrentDictionary<string, ConnectionContext> _connections = new();
    private readonly ConcurrentDictionary<string, PendingTransaction> _pendingTransactions = new();
    private readonly Timer _cleanupTimer;
    private readonly ILogger<RequestResponsePairingEngine> _logger;
    private readonly TimeSpan _transactionTimeout;
    private readonly object _globalLock = new();
    private long _globalSequenceNumber;
    private bool _disposed;

    // HIGH-THROUGHPUT OPTIMIZATIONS - Performance tracking
    private long _totalPaired;
    private long _totalTimeouts;
    private long _totalRequests;
    private readonly object _statsLock = new();

    public RequestResponsePairingEngine(ILogger<RequestResponsePairingEngine> logger, TimeSpan? transactionTimeout = null)
    {
        _logger = logger;
        _transactionTimeout = transactionTimeout ?? TimeSpan.FromMinutes(2);
        _cleanupTimer = new Timer(CleanupExpiredTransactions, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        _globalSequenceNumber = 0;

        _logger.LogInformation("🔄 FIFO pairing engine initialized with {Timeout}s timeout", _transactionTimeout.TotalSeconds);
    }

    public event EventHandler<TransactionPairedEventArgs>? TransactionPaired;
    public event EventHandler<TransactionTimeoutEventArgs>? TransactionTimeout;
    public event EventHandler<PairingStatisticsEventArgs>? StatisticsUpdated;

    /// <summary>
    /// Creates a new connection context with guaranteed unique ID and proper initialization
    /// </summary>
    public string CreateConnection(string host, int port, string protocol = "HTTPS")
    {
        if (_disposed) return string.Empty;

        var connectionId = GenerateConnectionId(host, port);
        var globalSequence = Interlocked.Increment(ref _globalSequenceNumber);
        var context = new ConnectionContext(connectionId, host, port, protocol, globalSequence);

        if (_connections.TryAdd(connectionId, context))
        {
            _logger.LogInformation("🔗 Created connection {ConnectionId} for {Host}:{Port} (Global#{GlobalSeq})",
                connectionId, host, port, globalSequence);
            return connectionId;
        }

        _logger.LogWarning("⚠️ Failed to create connection {ConnectionId} - already exists", connectionId);
        return string.Empty;
    }

    /// <summary>
    /// HIGH-PERFORMANCE: Records a request with atomic FIFO ordering and optimized correlation ID generation
    /// </summary>
    public string RecordRequest(string connectionId, IHttpInterceptedRequestMessage request)
    {
        if (_disposed) return string.Empty;

        if (!_connections.TryGetValue(connectionId, out var connection))
        {
            _logger.LogWarning("⚠️ Connection {ConnectionId} not found for request", connectionId);
            return string.Empty;
        }

        // OPTIMIZATION: Pre-generate transaction ID to reduce lock time
        var sequenceNumber = connection.GetNextSequenceNumber();
        var transactionId = GenerateTransactionId(request, connection, sequenceNumber);

        var transaction = new PendingTransaction
        {
            TransactionId = transactionId,
            ConnectionId = connectionId,
            Request = request,
            Hostname = connection.Hostname,
            Port = connection.Port,
            Protocol = connection.Protocol,
            StartTime = DateTime.UtcNow,
            SequenceNumber = sequenceNumber
        };

        // OPTIMIZATION: Atomic operations with minimal lock time
        if (_pendingTransactions.TryAdd(transactionId, transaction))
        {
            connection.EnqueueRequest(transactionId);

            // OPTIMIZATION: Update statistics atomically
            Interlocked.Increment(ref _totalRequests);

            _logger.LogDebug("📝 Recorded request {TransactionId} (Seq#{Seq}) for connection {ConnectionId}: {Method} {Uri}",
                transactionId, sequenceNumber, connectionId, request.Method, request.Uri);

            return transactionId;
        }

        _logger.LogError("❌ Failed to record request - transaction ID collision: {TransactionId}", transactionId);
        return string.Empty;
    }

    /// <summary>
    /// Peeks at the next transaction ID for a connection without removing it from the queue
    /// Used for breakpoint processing before response pairing
    /// </summary>
    public string? PeekNextTransactionId(string connectionId) {
        if (_disposed) return null;

        if (!_connections.TryGetValue(connectionId, out var connection)) {
            return null;
        }

        return connection.PeekNextRequest();
    }

    /// <summary>
    /// HIGH-PERFORMANCE: Pairs response with request using optimized FIFO dequeue and atomic operations
    /// </summary>
    public bool PairResponse(string connectionId, IHttpInterceptedResponseMessage response)
    {
        if (_disposed) return false;

        if (!_connections.TryGetValue(connectionId, out var connection))
        {
            _logger.LogWarning("⚠️ Connection {ConnectionId} not found for response pairing", connectionId);
            return false;
        }

        // OPTIMIZATION: Fast-path dequeue with minimal lock time
        var transactionId = connection.DequeueNextRequest();
        if (string.IsNullOrEmpty(transactionId))
        {
            _logger.LogWarning("❌ No pending requests for response on connection {ConnectionId}", connectionId);
            return false;
        }

        // OPTIMIZATION: Atomic remove with race condition recovery
        if (!_pendingTransactions.TryRemove(transactionId, out var transaction))
        {
            _logger.LogError("⚠️ Transaction {TransactionId} not found in pending transactions - possible race condition", transactionId);
            // OPTIMIZATION: Re-enqueue with minimal overhead
            connection.EnqueueRequest(transactionId);
            return false;
        }

        // OPTIMIZATION: Pre-calculate end time to reduce object creation time
        var endTime = DateTime.UtcNow;
        var duration = endTime - transaction.StartTime;

        var pairedTransaction = new HttpTransaction
        {
            TransactionId = transactionId,
            Hostname = transaction.Hostname,
            Port = transaction.Port,
            Protocol = transaction.Protocol,
            StartTime = transaction.StartTime,
            EndTime = endTime,
            Request = transaction.Request,
            Response = response
        };

        // OPTIMIZATION: Update statistics atomically
        Interlocked.Increment(ref _totalPaired);

        _logger.LogInformation("✅ Paired response with request {TransactionId} (Seq#{Seq}) for connection {ConnectionId} in {Duration}ms",
            transactionId, transaction.SequenceNumber, connectionId, duration.TotalMilliseconds);

        // OPTIMIZATION: Fire event asynchronously to avoid blocking pairing
        _ = Task.Run(() => TransactionPaired?.Invoke(this, new TransactionPairedEventArgs(pairedTransaction)));
        return true;
    }

    public void CloseConnection(string connectionId)
    {
        if (_connections.TryRemove(connectionId, out var connection))
        {
            var pendingRequests = connection.GetPendingRequestIds();
            if (pendingRequests.Count > 0)
            {
                _logger.LogWarning("🧹 Connection {ConnectionId} closed with {PendingCount} unmatched requests",
                    connectionId, pendingRequests.Count);

                foreach (var transactionId in pendingRequests)
                {
                    if (_pendingTransactions.TryRemove(transactionId, out var transaction))
                    {
                        TransactionTimeout?.Invoke(this, new TransactionTimeoutEventArgs(transaction.TransactionId, "Connection closed"));
                    }
                }
            }
            connection.Dispose();
        }
    }

    /// <summary>
    /// HIGH-PERFORMANCE: Get comprehensive statistics with minimal overhead
    /// </summary>
    public (int ActiveConnections, int PendingTransactions, long TotalPaired, long TotalTimeouts, long TotalRequests) GetStatistics() {
        return (
            _connections.Count,
            _pendingTransactions.Count,
            Interlocked.Read(ref _totalPaired),
            Interlocked.Read(ref _totalTimeouts),
            Interlocked.Read(ref _totalRequests)
        );
    }

    /// <summary>
    /// HIGH-PERFORMANCE: Get detailed per-connection statistics
    /// </summary>
    public Dictionary<string, (int PendingRequests, long Sequence, TimeSpan Age)> GetConnectionStatistics() {
        var result = new Dictionary<string, (int, long, TimeSpan)>();
        var now = DateTime.UtcNow;

        foreach (var (connectionId, connection) in _connections) {
            result[connectionId] = (
                connection.GetPendingRequestCount(),
                connection.CurrentSequence,
                now - connection.CreatedAt
            );
        }

        return result;
    }

    private static string GenerateConnectionId(string host, int port)
    {
        return $"{host}:{port}:{Guid.NewGuid():N}";
    }

    private static string GenerateTransactionId(IHttpInterceptedRequestMessage request, ConnectionContext connection, long sequenceNumber)
    {
        var method = request.Method ?? "UNKNOWN";
        var path = request.Uri?.PathAndQuery ?? "";
        var timestamp = DateTime.UtcNow.Ticks;

        // Use the provided sequence number for guaranteed ordering
        return $"{connection.ConnectionId}:{method}:{path.GetHashCode():X}:{sequenceNumber}:{timestamp}";
    }

    /// <summary>
    /// OPTIMIZATION: Efficient cleanup with batched operations and minimal lock contention
    /// </summary>
    private void CleanupExpiredTransactions(object? state)
    {
        if (_disposed) return;

        var cutoffTime = DateTime.UtcNow.Subtract(_transactionTimeout);
        var expiredTransactions = _pendingTransactions.Values
            .Where(t => t.StartTime < cutoffTime)
            .ToList();

        // OPTIMIZATION: Batch process expired transactions
        var timeoutCount = 0;
        foreach (var transaction in expiredTransactions)
        {
            if (_pendingTransactions.TryRemove(transaction.TransactionId, out _))
            {
                if (_connections.TryGetValue(transaction.ConnectionId, out var connection))
                {
                    connection.RemoveRequest(transaction.TransactionId);
                }

                TransactionTimeout?.Invoke(this, new TransactionTimeoutEventArgs(transaction.TransactionId, "Transaction timeout"));
                timeoutCount++;
            }
        }

        // OPTIMIZATION: Update timeout statistics atomically and log once
        if (timeoutCount > 0)
        {
            Interlocked.Add(ref _totalTimeouts, timeoutCount);
            _logger.LogWarning("⏰ {Count} transactions timed out after {Timeout}s",
                timeoutCount, _transactionTimeout.TotalSeconds);
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _cleanupTimer?.Dispose();

        // OPTIMIZATION: Parallel disposal of connections for faster shutdown
        Parallel.ForEach(_connections.Values, connection => connection.Dispose());

        _connections.Clear();
        _pendingTransactions.Clear();

        // Log final statistics for performance analysis
        var (activeConnections, pendingTransactions, totalPaired, totalTimeouts, totalRequests) = GetStatistics();
        _logger.LogInformation("📊 Final pairing statistics: {TotalRequests} requests, {TotalPaired} paired, {TotalTimeouts} timeouts",
            totalRequests, totalPaired, totalTimeouts);
    }
}

/// <summary>
/// HIGH-PERFORMANCE thread-safe connection context that maintains FIFO ordering with optimized lock usage
/// </summary>
public sealed class ConnectionContext : IDisposable
{
    private readonly Queue<string> _requestQueue = new();
    private readonly Lock _lock = new();
    private long _sequenceNumber;
    private bool _disposed;

    // OPTIMIZATION: Cache frequently accessed values
    public string ConnectionId { get; }
    public string Hostname { get; }
    public int Port { get; }
    public string Protocol { get; }
    public DateTime CreatedAt { get; } = DateTime.UtcNow;
    public long GlobalSequence { get; }
    public long CurrentSequence => Interlocked.Read(ref _sequenceNumber);

    public ConnectionContext(string connectionId, string hostname, int port, string protocol, long globalSequence)
    {
        ConnectionId = connectionId;
        Hostname = hostname;
        Port = port;
        Protocol = protocol;
        GlobalSequence = globalSequence;
        _sequenceNumber = 0;
    }

    public long GetNextSequenceNumber()
    {
        return Interlocked.Increment(ref _sequenceNumber);
    }

    public void EnqueueRequest(string transactionId)
    {
        if (_disposed) return;

        lock (_lock)
        {
            _requestQueue.Enqueue(transactionId);
        }
    }

    public string? DequeueNextRequest()
    {
        if (_disposed) return null;

        lock (_lock)
        {
            return _requestQueue.Count > 0 ? _requestQueue.Dequeue() : null;
        }
    }

    /// <summary>
    /// Peeks at the next request transaction ID without removing it from the queue
    /// Used for breakpoint processing before response pairing
    /// </summary>
    public string? PeekNextRequest() {
        if (_disposed) return null;

        lock (_lock) {
            return _requestQueue.Count > 0 ? _requestQueue.Peek() : null;
        }
    }

    /// <summary>
    /// OPTIMIZATION: Removes specific transaction ID from queue with minimal allocations
    /// Note: This is rarely used in normal flow (only for cleanup), so optimization focuses on correctness
    /// </summary>
    public void RemoveRequest(string transactionId)
    {
        if (_disposed) return;

        lock (_lock)
        {
            // OPTIMIZATION: Use array-based approach to reduce allocations
            var items = _requestQueue.ToArray();
            _requestQueue.Clear();

            foreach (var id in items) {
                if (id != transactionId)
                {
                    _requestQueue.Enqueue(id);
                }
            }
        }
    }

    /// <summary>
    /// OPTIMIZATION: Fast snapshot of pending requests with minimal lock time
    /// </summary>
    public List<string> GetPendingRequestIds() {
        if (_disposed) return [];

        lock (_lock) {
            // OPTIMIZATION: Pre-allocate list with known capacity
            var result = new List<string>(_requestQueue.Count);
            result.AddRange(_requestQueue);
            return result;
        }
    }

    /// <summary>
    /// OPTIMIZATION: Get queue count without full enumeration
    /// </summary>
    public int GetPendingRequestCount() {
        if (_disposed) return 0;

        lock (_lock)
        {
            return _requestQueue.Count;
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        lock (_lock)
        {
            _requestQueue.Clear();
        }
    }
}

public sealed class PendingTransaction
{
    public string TransactionId { get; init; } = string.Empty;
    public string ConnectionId { get; init; } = string.Empty;
    public IHttpInterceptedRequestMessage? Request { get; init; }
    public string Hostname { get; init; } = string.Empty;
    public int Port { get; init; }
    public string Protocol { get; init; } = string.Empty;
    public DateTime StartTime { get; init; }
    public long SequenceNumber { get; init; }
}

public sealed class PairingStatisticsEventArgs(int activeConnections, int pendingTransactions, int totalPaired, int totalTimeouts) : EventArgs
{
    public int ActiveConnections { get; } = activeConnections;
    public int PendingTransactions { get; } = pendingTransactions;
    public int TotalPaired { get; } = totalPaired;
    public int TotalTimeouts { get; } = totalTimeouts;
}
