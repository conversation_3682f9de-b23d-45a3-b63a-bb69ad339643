using System.Collections.Concurrent;
using System.Diagnostics;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// FIFO-based request-response pairing engine that ensures 100% correct correlation
/// under high concurrency by maintaining strict per-connection ordering.
/// </summary>
public sealed class RequestResponsePairingEngine : IDisposable
{
    private readonly ConcurrentDictionary<string, ConnectionContext> _connections = new();
    private readonly ConcurrentDictionary<string, PendingTransaction> _pendingTransactions = new();
    private readonly Timer _cleanupTimer;
    private readonly ILogger<RequestResponsePairingEngine> _logger;
    private readonly TimeSpan _transactionTimeout;
    private readonly object _globalLock = new();
    private long _globalSequenceNumber;
    private bool _disposed;

    public RequestResponsePairingEngine(ILogger<RequestResponsePairingEngine> logger, TimeSpan? transactionTimeout = null)
    {
        _logger = logger;
        _transactionTimeout = transactionTimeout ?? TimeSpan.FromMinutes(2);
        _cleanupTimer = new Timer(CleanupExpiredTransactions, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        _globalSequenceNumber = 0;

        _logger.LogInformation("🔄 FIFO pairing engine initialized with {Timeout}s timeout", _transactionTimeout.TotalSeconds);
    }

    public event EventHandler<TransactionPairedEventArgs>? TransactionPaired;
    public event EventHandler<TransactionTimeoutEventArgs>? TransactionTimeout;
    public event EventHandler<PairingStatisticsEventArgs>? StatisticsUpdated;

    /// <summary>
    /// Creates a new connection context with guaranteed unique ID and proper initialization
    /// </summary>
    public string CreateConnection(string host, int port, string protocol = "HTTPS")
    {
        if (_disposed) return string.Empty;

        var connectionId = GenerateConnectionId(host, port);
        var globalSequence = Interlocked.Increment(ref _globalSequenceNumber);
        var context = new ConnectionContext(connectionId, host, port, protocol, globalSequence);

        if (_connections.TryAdd(connectionId, context))
        {
            _logger.LogInformation("🔗 Created connection {ConnectionId} for {Host}:{Port} (Global#{GlobalSeq})",
                connectionId, host, port, globalSequence);
            return connectionId;
        }

        _logger.LogWarning("⚠️ Failed to create connection {ConnectionId} - already exists", connectionId);
        return string.Empty;
    }

    /// <summary>
    /// Records a request with atomic FIFO ordering and guaranteed correlation ID generation
    /// </summary>
    public string RecordRequest(string connectionId, IHttpInterceptedRequestMessage request)
    {
        if (_disposed) return string.Empty;

        if (!_connections.TryGetValue(connectionId, out var connection))
        {
            _logger.LogWarning("⚠️ Connection {ConnectionId} not found for request", connectionId);
            return string.Empty;
        }

        // Generate transaction ID with atomic sequence number
        var sequenceNumber = connection.GetNextSequenceNumber();
        var transactionId = GenerateTransactionId(request, connection, sequenceNumber);

        var transaction = new PendingTransaction
        {
            TransactionId = transactionId,
            ConnectionId = connectionId,
            Request = request,
            Hostname = connection.Hostname,
            Port = connection.Port,
            Protocol = connection.Protocol,
            StartTime = DateTime.UtcNow,
            SequenceNumber = sequenceNumber
        };

        // Atomic operations: add to pending transactions and enqueue in connection
        if (_pendingTransactions.TryAdd(transactionId, transaction))
        {
            connection.EnqueueRequest(transactionId);

            _logger.LogDebug("📝 Recorded request {TransactionId} (Seq#{Seq}) for connection {ConnectionId}: {Method} {Uri}",
                transactionId, sequenceNumber, connectionId, request.Method, request.Uri);

            return transactionId;
        }

        _logger.LogError("❌ Failed to record request - transaction ID collision: {TransactionId}", transactionId);
        return string.Empty;
    }

    public bool PairResponse(string connectionId, IHttpInterceptedResponseMessage response)
    {
        if (_disposed) return false;

        if (!_connections.TryGetValue(connectionId, out var connection))
        {
            _logger.LogWarning("⚠️ Connection {ConnectionId} not found for response pairing", connectionId);
            return false;
        }

        var transactionId = connection.DequeueNextRequest();
        if (string.IsNullOrEmpty(transactionId))
        {
            _logger.LogWarning("❌ No pending requests for response on connection {ConnectionId}", connectionId);
            return false;
        }

        // Remove from pending transactions atomically
        if (!_pendingTransactions.TryRemove(transactionId, out var transaction))
        {
            _logger.LogError("⚠️ Transaction {TransactionId} not found in pending transactions - possible race condition", transactionId);
            // Try to re-enqueue the transaction ID back to the connection
            connection.EnqueueRequest(transactionId);
            return false;
        }

        var pairedTransaction = new HttpTransaction
        {
            TransactionId = transactionId,
            Hostname = transaction.Hostname,
            Port = transaction.Port,
            Protocol = transaction.Protocol,
            StartTime = transaction.StartTime,
            EndTime = DateTime.UtcNow,
            Request = transaction.Request,
            Response = response
        };

        _logger.LogInformation("✅ Paired response with request {TransactionId} (Seq#{Seq}) for connection {ConnectionId}",
            transactionId, transaction.SequenceNumber, connectionId);

        TransactionPaired?.Invoke(this, new TransactionPairedEventArgs(pairedTransaction));
        return true;
    }

    public void CloseConnection(string connectionId)
    {
        if (_connections.TryRemove(connectionId, out var connection))
        {
            var pendingRequests = connection.GetPendingRequestIds();
            if (pendingRequests.Count > 0)
            {
                _logger.LogWarning("🧹 Connection {ConnectionId} closed with {PendingCount} unmatched requests",
                    connectionId, pendingRequests.Count);

                foreach (var transactionId in pendingRequests)
                {
                    if (_pendingTransactions.TryRemove(transactionId, out var transaction))
                    {
                        TransactionTimeout?.Invoke(this, new TransactionTimeoutEventArgs(transaction.TransactionId, "Connection closed"));
                    }
                }
            }
            connection.Dispose();
        }
    }

    public (int ActiveConnections, int PendingTransactions) GetStatistics()
    {
        return (_connections.Count, _pendingTransactions.Count);
    }

    private static string GenerateConnectionId(string host, int port)
    {
        return $"{host}:{port}:{Guid.NewGuid():N}";
    }

    private static string GenerateTransactionId(IHttpInterceptedRequestMessage request, ConnectionContext connection, long sequenceNumber)
    {
        var method = request.Method ?? "UNKNOWN";
        var path = request.Uri?.PathAndQuery ?? "";
        var timestamp = DateTime.UtcNow.Ticks;

        // Use the provided sequence number for guaranteed ordering
        return $"{connection.ConnectionId}:{method}:{path.GetHashCode():X}:{sequenceNumber}:{timestamp}";
    }

    private void CleanupExpiredTransactions(object? state)
    {
        if (_disposed) return;

        var expiredTransactions = _pendingTransactions.Values
            .Where(t => DateTime.UtcNow - t.StartTime > _transactionTimeout)
            .ToList();

        foreach (var transaction in expiredTransactions)
        {
            if (_pendingTransactions.TryRemove(transaction.TransactionId, out _))
            {
                if (_connections.TryGetValue(transaction.ConnectionId, out var connection))
                {
                    connection.RemoveRequest(transaction.TransactionId);
                }

                _logger.LogWarning("⏰ Transaction {TransactionId} timed out after {Timeout}",
                    transaction.TransactionId, _transactionTimeout);

                TransactionTimeout?.Invoke(this, new TransactionTimeoutEventArgs(transaction.TransactionId, "Transaction timeout"));
            }
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _cleanupTimer?.Dispose();
        
        foreach (var connection in _connections.Values)
        {
            connection.Dispose();
        }
        
        _connections.Clear();
        _pendingTransactions.Clear();
    }
}

/// <summary>
/// Thread-safe connection context that maintains FIFO ordering of requests
/// </summary>
public sealed class ConnectionContext : IDisposable
{
    private readonly Queue<string> _requestQueue = new();
    private readonly Lock _lock = new();
    private long _sequenceNumber;
    private bool _disposed;

    public string ConnectionId { get; }
    public string Hostname { get; }
    public int Port { get; }
    public string Protocol { get; }
    public DateTime CreatedAt { get; } = DateTime.UtcNow;
    public long GlobalSequence { get; }
    public long CurrentSequence => _sequenceNumber;

    public ConnectionContext(string connectionId, string hostname, int port, string protocol, long globalSequence)
    {
        ConnectionId = connectionId;
        Hostname = hostname;
        Port = port;
        Protocol = protocol;
        GlobalSequence = globalSequence;
        _sequenceNumber = 0;
    }

    public long GetNextSequenceNumber()
    {
        return Interlocked.Increment(ref _sequenceNumber);
    }

    public void EnqueueRequest(string transactionId)
    {
        if (_disposed) return;

        lock (_lock)
        {
            _requestQueue.Enqueue(transactionId);
        }
    }

    public string? DequeueNextRequest()
    {
        if (_disposed) return null;

        lock (_lock)
        {
            return _requestQueue.Count > 0 ? _requestQueue.Dequeue() : null;
        }
    }

    public void RemoveRequest(string transactionId)
    {
        if (_disposed) return;

        lock (_lock)
        {
            var tempQueue = new Queue<string>();
            while (_requestQueue.Count > 0)
            {
                var id = _requestQueue.Dequeue();
                if (id != transactionId)
                {
                    tempQueue.Enqueue(id);
                }
            }

            while (tempQueue.Count > 0)
            {
                _requestQueue.Enqueue(tempQueue.Dequeue());
            }
        }
    }

    public List<string> GetPendingRequestIds()
    {
        if (_disposed) return [];

        lock (_lock)
        {
            return _requestQueue.ToList();
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        lock (_lock)
        {
            _requestQueue.Clear();
        }
    }
}

public sealed class PendingTransaction
{
    public string TransactionId { get; init; } = string.Empty;
    public string ConnectionId { get; init; } = string.Empty;
    public IHttpInterceptedRequestMessage? Request { get; init; }
    public string Hostname { get; init; } = string.Empty;
    public int Port { get; init; }
    public string Protocol { get; init; } = string.Empty;
    public DateTime StartTime { get; init; }
    public long SequenceNumber { get; init; }
}

public sealed class PairingStatisticsEventArgs(int activeConnections, int pendingTransactions, int totalPaired, int totalTimeouts) : EventArgs
{
    public int ActiveConnections { get; } = activeConnections;
    public int PendingTransactions { get; } = pendingTransactions;
    public int TotalPaired { get; } = totalPaired;
    public int TotalTimeouts { get; } = totalTimeouts;
}
