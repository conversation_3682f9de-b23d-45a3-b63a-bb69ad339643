using System.Net.Sockets;
using System.Text;
using System.Text.RegularExpressions;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class HttpDataRelayService : IDisposable
{
    private readonly HttpTransactionManager _transactionManager;
    private readonly AmfService _amfService;
    private readonly ParallelProcessingEngine _processingEngine;
    private readonly BreakpointManager _breakpointManager;
    private readonly ILogger<HttpDataRelayService> _logger;
    // THREAD-SAFE: Atomic counters for statistics
    private long _successfulPairings = 0;
    private long _failedPairings = 0;
    private bool _disposed;

    public HttpDataRelayService(
        HttpTransactionManager transactionManager,
        AmfService amfService,
        ParallelProcessingEngine processingEngine,
        BreakpointManager breakpointManager,
        ILogger<HttpDataRelayService> logger)
    {
        _transactionManager = transactionManager;
        _amfService = amfService;
        _processingEngine = processingEngine;
        _breakpointManager = breakpointManager;
        _logger = logger;
    }

    public async Task RelayDataAsync(Stream from, Stream to, string connectionId, string direction, string host = "unknown", int port = 443)
    {
        try
        {
            var buffer = new byte[8192];
            var messageBuffer = new List<byte>();

            while (true)
            {
                var received = await from.ReadAsync(buffer.AsMemory()).ConfigureAwait(false);
                if (received == 0) break;

                messageBuffer.AddRange(buffer.Take(received));

                // Process HTTP messages BEFORE forwarding data to check for breakpoints
                var shouldContinue = await TryProcessHttpMessagesWithBreakpoint(messageBuffer, connectionId, direction, buffer.AsMemory(0, received), to).ConfigureAwait(false);

                if (!shouldContinue) {
                    // Breakpoint cancelled the request - stop processing
                    break;
                }
            }
        }
        catch (ObjectDisposedException)
        {
            // Stream was disposed - this is normal when connections close
            _logger.LogDebug("🔌 Stream disposed for connection {ConnectionId} ({Direction})", connectionId, direction);
        }
        catch (IOException ioEx) when (ioEx.InnerException is SocketException socketEx && socketEx.ErrorCode == 995)
        {
            // Connection was aborted - this is normal when connections close
            _logger.LogDebug("🔌 Connection {ConnectionId} closed ({Direction})", connectionId, direction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error in data relay for connection {ConnectionId}, direction {Direction}",
                connectionId, direction);
        }
    }

    private async Task<bool> TryProcessHttpMessagesWithBreakpoint(List<byte> messageBuffer, string connectionId, string direction, ReadOnlyMemory<byte> currentData, Stream targetStream) {
        try {
            var data = messageBuffer.ToArray();
            var messageText = Encoding.UTF8.GetString(data);

            if (direction == "Server->Client" && messageText.StartsWith("HTTP/")) {
                // Check if we have a complete HTTP response
                if (IsCompleteHttpResponse(data)) {
                    // Process response with breakpoint BEFORE forwarding
                    var (shouldContinue, modifiedData) = await ProcessHttpResponseWithBreakpoint(data, connectionId).ConfigureAwait(false);
                    if (!shouldContinue)
                        return false;

                    // Forward modified data (or original if no modifications) after breakpoint processing
                    var dataToForward = modifiedData ?? data;
                    await targetStream.WriteAsync(dataToForward).ConfigureAwait(false);
                    messageBuffer.Clear();
                }
                else {
                    // Incomplete response - forward data and keep buffering
                    await targetStream.WriteAsync(currentData).ConfigureAwait(false);
                }
            }
            else if (direction == "Client->Server" && IsHttpRequest(messageText)) {
                // Check if we have a complete HTTP request
                if (IsCompleteHttpRequest(data)) {
                    // Process request with breakpoint BEFORE forwarding
                    var shouldContinue = await ProcessHttpRequestWithBreakpoint(data, connectionId).ConfigureAwait(false);
                    if (!shouldContinue)
                        return false;

                    // Forward data after breakpoint processing
                    await targetStream.WriteAsync(currentData).ConfigureAwait(false);
                    messageBuffer.Clear();
                }
                else {
                    // Incomplete request - forward data and keep buffering
                    await targetStream.WriteAsync(currentData).ConfigureAwait(false);
                }
            }
            else {
                // Not HTTP or incomplete - forward data
                await targetStream.WriteAsync(currentData).ConfigureAwait(false);

                if (messageBuffer.Count > 1024 * 1024) {
                    messageBuffer.Clear();
                }
            }

            return true;
        }
        catch (Exception ex) {
            _logger.LogError(ex, "❌ Error processing HTTP messages for connection {ConnectionId}", connectionId);
            // Forward data even on error
            await targetStream.WriteAsync(currentData).ConfigureAwait(false);
            return true;
        }
    }



    private async Task<bool> ProcessHttpRequestWithBreakpoint(byte[] data, string connectionId) {
        try {
            // Extract host and port from connectionId (format: host:port:guid)
            var parts = connectionId.Split(':');
            var host = parts.Length > 0 ? parts[0] : "unknown";
            var port = parts.Length > 1 && int.TryParse(parts[1], out var p) ? p : 443;

            var requestMessage = HttpMessageParser.ParseRequest(data, host, port, "https");
            if (requestMessage == null)
                return true; // Continue if parsing failed

            // Check for AMF content
            if (_amfService.IsAmfContent(requestMessage)) {
                _logger.LogInformation("🔥 AMF3 DETECTED in HTTPS REQUEST for connection {ConnectionId}!", connectionId);

                var decodedAmf = await _amfService.DecodeAmfRequestAsync(requestMessage).ConfigureAwait(false);
                if (decodedAmf != null) {
                    _logger.LogDebug("✅ AMF request decoded successfully for {ConnectionId}", connectionId);
                    requestMessage = new HttpInterceptedRequestMessage {
                        Method = requestMessage.Method,
                        Uri = requestMessage.Uri,
                        Version = requestMessage.Version,
                        Headers = requestMessage.Headers,
                        Content = requestMessage.Content,
                        IsAmf = true,
                        Amf = decodedAmf
                    };
                }
                else {
                    _logger.LogWarning("❌ AMF request decode failed for {ConnectionId}", connectionId);
                    // Still mark as AMF even if decode failed
                    requestMessage = new HttpInterceptedRequestMessage {
                        Method = requestMessage.Method,
                        Uri = requestMessage.Uri,
                        Version = requestMessage.Version,
                        Headers = requestMessage.Headers,
                        Content = requestMessage.Content,
                        IsAmf = true,
                        Amf = null
                    };
                }
            }

            // Start transaction BEFORE breakpoint processing to get proper transaction ID
            var transactionId = _transactionManager.StartTransaction(connectionId, requestMessage);

            // Check for request breakpoint with proper transaction ID
            var breakpointAction = await _breakpointManager.CheckRequestBreakpointAsync(
                transactionId, // FIXED: Use proper transaction ID instead of temporary GUID
                connectionId,
                host,
                port,
                "HTTPS",
                requestMessage).ConfigureAwait(false);

            // Handle breakpoint action
            if (breakpointAction.HasValue) {
                if (breakpointAction.Value == BreakpointAction.Cancel) {
                    _logger.LogInformation("🔴 Request cancelled by user via breakpoint for {ConnectionId}", connectionId);
                    return false; // Don't continue processing
                }
                _logger.LogDebug("✅ Request breakpoint resolved with {Action}, continuing with {ConnectionId}",
                    breakpointAction.Value, connectionId);
            }

            _logger.LogDebug("📝 Processed HTTP request {TransactionId} on connection {ConnectionId}: {Method} {Uri}",
                transactionId, connectionId, requestMessage.Method, requestMessage.Uri);

            return true; // Continue processing
        }
        catch (Exception ex) {
            _logger.LogError(ex, "❌ Error processing HTTP request with breakpoint for {ConnectionId}", connectionId);
            return true; // Continue on error
        }
    }

    private async Task<(bool shouldContinue, byte[]? modifiedData)> ProcessHttpResponseWithBreakpoint(byte[] data, string connectionId) {
        try {
            // Extract host and port from connectionId
            var parts = connectionId.Split(':');
            var host = parts.Length > 0 ? parts[0] : "unknown";
            var port = parts.Length > 1 && int.TryParse(parts[1], out var p) ? p : 443;

            var responseMessage = HttpMessageParser.ParseResponse(data);
            if (responseMessage == null)
                return (true, null); // Continue if parsing failed

            // Check for AMF content
            if (_amfService.IsAmfContent(responseMessage)) {
                _logger.LogInformation("🔥 AMF3 DETECTED in HTTPS RESPONSE for connection {ConnectionId}!", connectionId);

                var url = $"https://{host}:{port}";
                var decodedAmf = await _amfService.DecodeAmfResponseAsync(responseMessage, url).ConfigureAwait(false);
                if (decodedAmf != null) {
                    _logger.LogDebug("✅ AMF response decoded successfully for {ConnectionId}", connectionId);
                    responseMessage = new HttpInterceptedResponseMessage {
                        StatusCode = responseMessage.StatusCode,
                        ReasonPhrase = responseMessage.ReasonPhrase,
                        Version = responseMessage.Version,
                        Headers = responseMessage.Headers,
                        Content = responseMessage.Content,
                        Method = responseMessage.Method,
                        Uri = responseMessage.Uri,
                        IsAmf = true,
                        Amf = decodedAmf
                    };
                }
                else {
                    _logger.LogWarning("❌ AMF response decode failed for {ConnectionId}", connectionId);
                    // Still mark as AMF even if decode failed
                    responseMessage = new HttpInterceptedResponseMessage {
                        StatusCode = responseMessage.StatusCode,
                        ReasonPhrase = responseMessage.ReasonPhrase,
                        Version = responseMessage.Version,
                        Headers = responseMessage.Headers,
                        Content = responseMessage.Content,
                        Method = responseMessage.Method,
                        Uri = responseMessage.Uri,
                        IsAmf = true,
                        Amf = null
                    };
                }
            }

            // Get the proper transaction ID for response breakpoint processing
            var transactionId = _transactionManager.PeekNextTransactionId(connectionId);
            if (string.IsNullOrEmpty(transactionId)) {
                _logger.LogWarning("⚠️ No pending transaction found for response breakpoint on connection {ConnectionId}", connectionId);
                transactionId = $"unknown-{connectionId}-{DateTime.UtcNow.Ticks}"; // Fallback
            }

            // Check for response breakpoint with proper transaction ID
            var (breakpointAction, modifiedResponse) = await _breakpointManager.CheckResponseBreakpointAsync(
                transactionId, // FIXED: Use proper transaction ID instead of temporary GUID
                connectionId,
                host,
                port,
                "HTTPS",
                responseMessage).ConfigureAwait(false);

            byte[]? modifiedData = null;

            // Handle breakpoint action
            if (breakpointAction.HasValue) {
                if (breakpointAction.Value == BreakpointAction.Cancel) {
                    _logger.LogInformation("🔴 Response cancelled by user via breakpoint for {ConnectionId}", connectionId);
                    return (false, null); // Don't continue processing
                }
                _logger.LogDebug("✅ Response breakpoint resolved with {Action}, continuing with {ConnectionId}",
                    breakpointAction.Value, connectionId);

                // If Execute action was chosen and we have a modified response, convert it back to raw bytes
                if (breakpointAction.Value == BreakpointAction.Execute && modifiedResponse != null) {
                    modifiedData = ConvertResponseToBytes(modifiedResponse);
                    _logger.LogInformation("🔧 Using modified response data for {ConnectionId} (size: {Size} bytes)",
                        connectionId, modifiedData.Length);
                }
            }

            // Complete transaction using the FIFO pairing system (use modified response if available)
            var responseToComplete = modifiedResponse ?? responseMessage;
            var paired = await _transactionManager.CompleteTransactionAsync(connectionId, responseToComplete);
            if (paired) {
                _logger.LogDebug("✅ Processed HTTP response on connection {ConnectionId}", connectionId);
            }

            return (true, modifiedData); // Continue processing with modified data
        }
        catch (Exception ex) {
            _logger.LogError(ex, "❌ Error processing HTTP response with breakpoint for {ConnectionId}", connectionId);
            return (true, null); // Continue on error
        }
    }

    /// <summary>
    /// Converts an HTTP response message back to raw bytes for forwarding
    /// </summary>
    private static byte[] ConvertResponseToBytes(IHttpInterceptedResponseMessage response) {
        var responseBuilder = new StringBuilder();

        // Status line
        responseBuilder.AppendLine($"HTTP/{response.Version} {(int)response.StatusCode} {response.ReasonPhrase}");

        // Headers
        foreach (var header in response.Headers) {
            foreach (var value in header.Value) {
                responseBuilder.AppendLine($"{header.Key}: {value}");
            }
        }

        // Empty line between headers and body
        responseBuilder.AppendLine();

        // Convert headers to bytes
        var headerBytes = Encoding.UTF8.GetBytes(responseBuilder.ToString());

        // Combine headers and content
        if (response.Content?.Length > 0) {
            var result = new byte[headerBytes.Length + response.Content.Length];
            Array.Copy(headerBytes, 0, result, 0, headerBytes.Length);
            Array.Copy(response.Content, 0, result, headerBytes.Length, response.Content.Length);
            return result;
        }

        return headerBytes;
    }

    private static bool IsHttpRequest(string messageText)
    {
        return Regex.IsMatch(messageText, @"^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\s", RegexOptions.IgnoreCase);
    }

    private static bool IsCompleteHttpRequest(byte[] data)
    {
        // Find header end
        var headerEndIndex = FindHeaderEndIndex(data);
        if (headerEndIndex == -1) return false;

        // Extract headers
        var headerBytes = data[..headerEndIndex];
        var headerText = Encoding.UTF8.GetString(headerBytes);
        var headers = ParseHeaders(headerText);

        // Check if we have Content-Length header
        if (headers.TryGetValue("Content-Length", out var contentLengthValue) &&
            long.TryParse(contentLengthValue, out var contentLength))
        {
            var bodyStartIndex = headerEndIndex + GetHeaderSeparatorLength(data, headerEndIndex);
            var actualBodyLength = data.Length - bodyStartIndex;
            return actualBodyLength >= contentLength;
        }

        // If no Content-Length, assume complete (for GET requests, etc.)
        return true;
    }

    private static bool IsCompleteHttpResponse(byte[] data)
    {
        // Find header end
        var headerEndIndex = FindHeaderEndIndex(data);
        if (headerEndIndex == -1) return false;

        // Extract headers
        var headerBytes = data[..headerEndIndex];
        var headerText = Encoding.UTF8.GetString(headerBytes);
        var headers = ParseHeaders(headerText);

        // Check for chunked transfer encoding
        if (headers.TryGetValue("Transfer-Encoding", out var transferEncoding) &&
            transferEncoding.Contains("chunked", StringComparison.OrdinalIgnoreCase))
        {
            return IsCompleteChunkedResponse(data, headerEndIndex);
        }

        // Check if we have Content-Length header
        if (headers.TryGetValue("Content-Length", out var contentLengthValue) &&
            long.TryParse(contentLengthValue, out var contentLength))
        {
            var bodyStartIndex = headerEndIndex + GetHeaderSeparatorLength(data, headerEndIndex);
            var actualBodyLength = data.Length - bodyStartIndex;
            return actualBodyLength >= contentLength;
        }

        // If no Content-Length and no chunked encoding, assume complete
        return true;
    }

    private static Dictionary<string, string> ParseHeaders(string headerText)
    {
        var headers = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        var lines = headerText.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None);

        for (var i = 1; i < lines.Length; i++) // Skip status line
        {
            if (string.IsNullOrWhiteSpace(lines[i])) break;

            var colonIndex = lines[i].IndexOf(':');
            if (colonIndex > 0)
            {
                var headerName = lines[i][..colonIndex].Trim();
                var headerValue = lines[i][(colonIndex + 1)..].Trim();
                headers[headerName] = headerValue;
            }
        }

        return headers;
    }

    private static int FindHeaderEndIndex(byte[] data)
    {
        for (var i = 0; i < data.Length - 3; i++)
        {
            if (data[i] == '\r' && data[i + 1] == '\n' && data[i + 2] == '\r' && data[i + 3] == '\n')
                return i;
        }

        for (var i = 0; i < data.Length - 1; i++)
        {
            if (data[i] == '\n' && data[i + 1] == '\n')
                return i;
        }

        return -1;
    }

    private static int GetHeaderSeparatorLength(byte[] data, int headerEndIndex)
    {
        if (headerEndIndex + 3 < data.Length &&
            data[headerEndIndex] == '\r' && data[headerEndIndex + 1] == '\n' &&
            data[headerEndIndex + 2] == '\r' && data[headerEndIndex + 3] == '\n')
            return 4;

        if (headerEndIndex + 1 < data.Length &&
            data[headerEndIndex] == '\n' && data[headerEndIndex + 1] == '\n')
            return 2;

        return 0;
    }

    private static bool IsCompleteChunkedResponse(byte[] data, int headerEndIndex)
    {
        var bodyStartIndex = headerEndIndex + GetHeaderSeparatorLength(data, headerEndIndex);
        var bodyData = data[bodyStartIndex..];
        var bodyText = Encoding.UTF8.GetString(bodyData);

        // Check if we have the final chunk (0\r\n\r\n)
        return bodyText.Contains("0\r\n\r\n") || bodyText.Contains("0\n\n");
    }

    /// <summary>
    /// THREAD-SAFE: Gets current pairing statistics with atomic reads
    /// </summary>
    public (long SuccessfulPairings, long FailedPairings, int PendingConnections) GetPairingStatistics()
    {
        return (
            Interlocked.Read(ref _successfulPairings),
            Interlocked.Read(ref _failedPairings),
            0
        );
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        // Log final statistics
        var (successful, failed, pending) = GetPairingStatistics();
        _logger.LogInformation("📊 Final pairing statistics: {Successful} successful, {Failed} failed, {Pending} pending",
            successful, failed, pending);

        // Cleanup completed
    }
}
